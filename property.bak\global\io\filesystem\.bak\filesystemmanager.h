﻿#ifndef FILESYSTEMMANAGER_H
#define FILESYSTEMMANAGER_H

#include <QStorageInfo>
#include <QThread>
#include "fileinfogatherer.h"

namespace filesystem {

class FileSystemManager : public QObject
{
    Q_OBJECT
public:
    // ~FileSystemManager();

    FileSystemManager(const FileSystemManager&) = delete;
    FileSystemManager& operator=(const FileSystemManager&) = delete;

    static FileSystemManager& instance()
    {
        static FileSystemManager fsm;
        return fsm;
    }

    QVariantList getDriver();

    void getFileInfos(const QString& path);

signals:
    void load(const QString& path);
    void updates(const std::vector<QFileInfo>& updates);
    void loaded(const QString& path);
    // void initFileSystemRoot(const std::map<std::string, std::variant<int, std::string>> data);

private:
    FileSystemManager() = default;

    FileInfoGatherer* m_fileInfoGatherer;
};

}
#endif // FILESYSTEMMANAGER_H
