﻿#include "iodevice.h"

namespace filesystem {

IODevice::IODevice() : m_openMode(OpenMode::NotOpen) {}

IODevice::~IODevice() {}

IODevice::OpenMode IODevice::openMode() const
{
    return m_openMode;
}

std::vector<char> IODevice::readAll() {
    if (!isOpen()) {
        return {};
    }

    // Save current position
    int64_t currentPos = pos();
    if (currentPos < 0) {
        return {};
    }

    // Get size and allocate buffer
    int64_t deviceSize = size();
    if (deviceSize < 0) {
        // Size unknown, read in chunks
        std::vector<char> result;
        const int64_t chunkSize = 4096;
        std::vector<char> chunk(chunkSize);
        
        // Seek to beginning
        if (seek(0) < 0) {
            seek(currentPos);
            return {};
        }
        
        while (!atEnd()) {
            int64_t bytesRead = read(chunk.data(), chunkSize);
            if (bytesRead <= 0) {
                break;
            }
            result.insert(result.end(), chunk.begin(), chunk.begin() + bytesRead);
        }
        
        // Restore position
        seek(currentPos);
        return result;
    } else {
        // Size known, read all at once
        std::vector<char> result(deviceSize);
        
        // Seek to beginning
        if (seek(0) < 0) {
            seek(currentPos);
            return {};
        }
        
        int64_t bytesRead = read(result.data(), deviceSize);
        if (bytesRead < 0) {
            // Error
            seek(currentPos);
            return {};
        }
        
        // Resize to actual bytes read
        result.resize(bytesRead);
        
        // Restore position
        seek(currentPos);
        return result;
    }
}

std::string IODevice::readLine(int64_t maxSize) {
    if (!isOpen()) {
        return {};
    }

    std::string result;
    char c;
    int64_t bytesRead = 0;

    while ((!maxSize || bytesRead < maxSize) && read(&c, 1) == 1) {
        bytesRead++;
        if (c == '\n') {
            break;
        } else if (c != '\r') {
            result += c;
        }
    }

    return result;
}

int64_t IODevice::write(const std::string& data) {
    return write(data.c_str(), data.size());
}

void IODevice::setOpenMode(OpenMode mode) {
    m_openMode = mode;
}

} // namespace filesystem
