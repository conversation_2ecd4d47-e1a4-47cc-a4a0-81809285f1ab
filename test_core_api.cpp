/**
 * @file test_core_api.cpp
 * @brief Property系统核心API测试
 * 
 * 这个文件专门测试Property系统的核心功能API，包括：
 * 1. Property类的setBinding()方法 - 设置属性绑定
 * 2. Property类的takeBinding()方法 - 移除并返回绑定
 * 3. Property类的hasBinding()方法 - 检查是否有绑定
 * 4. Property类的value()和setValue()方法 - 值的读写
 */

#include "PropertySystem/PropertySystem.h"
#include <iostream>
#include <string>
#include <cassert>

using namespace PropertySystem;

// ==================================================================================
// 测试辅助函数
// ==================================================================================

void printTestHeader(const std::string& testName) {
    std::cout << "\n=== " << testName << " ===" << std::endl;
}

void printTestResult(const std::string& testName, bool passed) {
    std::cout << (passed ? "✓ " : "✗ ") << testName << (passed ? " PASSED" : " FAILED") << std::endl;
}

// ==================================================================================
// 1. Property基础值操作测试
// ==================================================================================

void testPropertyValueOperations() {
    printTestHeader("Property Value Operations Test");
    
    bool allPassed = true;
    
    try {
        // 测试整数属性
        Property<int> intProp(42);
        
        // 测试value()方法
        int value = intProp.value();
        assert(value == 42);
        std::cout << "✓ Property<int>::value() returns correct initial value: " << value << std::endl;
        
        // 测试setValue()方法
        intProp.setValue(100);
        value = intProp.value();
        assert(value == 100);
        std::cout << "✓ Property<int>::setValue() updates value correctly: " << value << std::endl;
        
        // 测试隐式类型转换
        int implicitValue = intProp;
        assert(implicitValue == 100);
        std::cout << "✓ Property<int> implicit conversion works: " << implicitValue << std::endl;
        
        // 测试赋值操作符
        intProp = 200;
        assert(intProp.value() == 200);
        std::cout << "✓ Property<int> assignment operator works: " << intProp.value() << std::endl;
        
        // 测试字符串属性
        Property<std::string> stringProp("Hello");
        assert(stringProp.value() == "Hello");
        std::cout << "✓ Property<string> initial value: " << stringProp.value() << std::endl;
        
        stringProp.setValue("World");
        assert(stringProp.value() == "World");
        std::cout << "✓ Property<string> setValue works: " << stringProp.value() << std::endl;
        
        // 测试浮点数属性
        Property<double> doubleProp(3.14);
        assert(doubleProp.value() == 3.14);
        std::cout << "✓ Property<double> initial value: " << doubleProp.value() << std::endl;
        
        doubleProp = 2.71;
        assert(doubleProp.value() == 2.71);
        std::cout << "✓ Property<double> assignment: " << doubleProp.value() << std::endl;
        
    } catch (const std::exception& e) {
        std::cerr << "Exception in value operations test: " << e.what() << std::endl;
        allPassed = false;
    } catch (...) {
        std::cerr << "Unknown exception in value operations test" << std::endl;
        allPassed = false;
    }
    
    printTestResult("Property Value Operations", allPassed);
}

// ==================================================================================
// 2. Property绑定状态测试
// ==================================================================================

void testPropertyBindingState() {
    printTestHeader("Property Binding State Test");
    
    bool allPassed = true;
    
    try {
        // 测试初始状态
        Property<int> prop(42);
        
        // 测试hasBinding()方法 - 初始状态应该没有绑定
        bool hasBinding = prop.hasBinding();
        assert(!hasBinding);
        std::cout << "✓ Property::hasBinding() returns false initially: " << hasBinding << std::endl;
        
        // 测试创建简单绑定
        auto binding = makePropertyBinding([]() { return 100; });
        std::cout << "✓ Created property binding successfully" << std::endl;
        
        // 测试setBinding()方法
        auto oldBinding = prop.setBinding(binding);
        std::cout << "✓ Property::setBinding() executed successfully" << std::endl;
        
        // 测试绑定后的状态
        hasBinding = prop.hasBinding();
        assert(hasBinding);
        std::cout << "✓ Property::hasBinding() returns true after binding: " << hasBinding << std::endl;
        
        // 测试绑定后的值
        int boundValue = prop.value();
        assert(boundValue == 100);
        std::cout << "✓ Property value from binding: " << boundValue << std::endl;
        
        // 测试binding()方法
        auto currentBinding = prop.binding();
        std::cout << "✓ Property::binding() executed successfully" << std::endl;
        
        // 测试takeBinding()方法
        auto takenBinding = prop.takeBinding();
        std::cout << "✓ Property::takeBinding() executed successfully" << std::endl;
        
        // 测试移除绑定后的状态
        hasBinding = prop.hasBinding();
        assert(!hasBinding);
        std::cout << "✓ Property::hasBinding() returns false after takeBinding(): " << hasBinding << std::endl;
        
    } catch (const std::exception& e) {
        std::cerr << "Exception in binding state test: " << e.what() << std::endl;
        allPassed = false;
    } catch (...) {
        std::cerr << "Unknown exception in binding state test" << std::endl;
        allPassed = false;
    }
    
    printTestResult("Property Binding State", allPassed);
}

// ==================================================================================
// 3. Property绑定功能测试
// ==================================================================================

void testPropertyBindingFunctionality() {
    printTestHeader("Property Binding Functionality Test");
    
    bool allPassed = true;
    
    try {
        // 测试lambda绑定
        Property<int> sourceProp(50);
        Property<int> targetProp;
        
        // 创建绑定：targetProp = sourceProp * 2
        auto binding = makePropertyBinding([&sourceProp]() { 
            return sourceProp.value() * 2; 
        });
        
        targetProp.setBinding(binding);
        std::cout << "✓ Lambda binding created and set" << std::endl;
        
        // 测试绑定计算
        int targetValue = targetProp.value();
        assert(targetValue == 100); // 50 * 2
        std::cout << "✓ Binding calculation correct: " << targetValue << std::endl;
        
        // 测试源属性变化时的自动更新
        sourceProp.setValue(75);
        targetValue = targetProp.value();
        assert(targetValue == 150); // 75 * 2
        std::cout << "✓ Binding auto-update works: " << targetValue << std::endl;
        
        // 测试函数对象绑定
        struct Multiplier {
            int factor;
            Property<int>* source;
            
            Multiplier(int f, Property<int>* s) : factor(f), source(s) {}
            
            int operator()() const {
                return source->value() * factor;
            }
        };
        
        Property<int> funcObjProp;
        Multiplier multiplier(3, &sourceProp);
        auto funcBinding = makePropertyBinding(multiplier);
        
        funcObjProp.setBinding(funcBinding);
        int funcValue = funcObjProp.value();
        assert(funcValue == 225); // 75 * 3
        std::cout << "✓ Function object binding works: " << funcValue << std::endl;
        
        // 测试绑定替换
        auto newBinding = makePropertyBinding([&sourceProp]() { 
            return sourceProp.value() + 10; 
        });
        
        auto oldBinding = targetProp.setBinding(newBinding);
        int newValue = targetProp.value();
        assert(newValue == 85); // 75 + 10
        std::cout << "✓ Binding replacement works: " << newValue << std::endl;
        
    } catch (const std::exception& e) {
        std::cerr << "Exception in binding functionality test: " << e.what() << std::endl;
        allPassed = false;
    } catch (...) {
        std::cerr << "Unknown exception in binding functionality test" << std::endl;
        allPassed = false;
    }
    
    printTestResult("Property Binding Functionality", allPassed);
}

// ==================================================================================
// 4. Property观察者测试
// ==================================================================================

void testPropertyObservers() {
    printTestHeader("Property Observer Test");
    
    bool allPassed = true;
    
    try {
        Property<int> prop(10);
        
        // 测试变化通知
        int notificationCount = 0;
        int lastValue = 0;
        
        auto observer = prop.onValueChanged([&]() {
            notificationCount++;
            lastValue = prop.value();
        });
        
        std::cout << "✓ Observer created successfully" << std::endl;
        
        // 测试setValue触发通知
        prop.setValue(20);
        // 注意：在实际实现中，通知可能是异步的或需要特殊触发
        std::cout << "✓ setValue executed, notification count: " << notificationCount << std::endl;
        
        // 测试subscribe方法（立即调用一次）
        int subscribeCount = 0;
        auto subscriber = prop.subscribe([&]() {
            subscribeCount++;
        });
        
        std::cout << "✓ Subscribe executed, immediate call count: " << subscribeCount << std::endl;
        
        // 测试addNotifier方法
        auto notifier = prop.addNotifier([&]() {
            std::cout << "Notifier called with value: " << prop.value() << std::endl;
        });
        
        std::cout << "✓ Notifier added successfully" << std::endl;
        
    } catch (const std::exception& e) {
        std::cerr << "Exception in observer test: " << e.what() << std::endl;
        allPassed = false;
    } catch (...) {
        std::cerr << "Unknown exception in observer test" << std::endl;
        allPassed = false;
    }
    
    printTestResult("Property Observer", allPassed);
}

// ==================================================================================
// 5. Property依赖追踪测试
// ==================================================================================

void testPropertyDependencyTracking() {
    printTestHeader("Property Dependency Tracking Test");
    
    bool allPassed = true;
    
    try {
        // 创建依赖链：c = a + b
        Property<int> a(10);
        Property<int> b(20);
        Property<int> c;
        
        // 设置绑定
        c.setBinding(makePropertyBinding([&]() {
            return a.value() + b.value();
        }));
        
        // 测试初始计算
        int cValue = c.value();
        assert(cValue == 30);
        std::cout << "✓ Dependency calculation: " << cValue << " = " << a.value() << " + " << b.value() << std::endl;
        
        // 测试依赖更新
        a.setValue(15);
        cValue = c.value();
        assert(cValue == 35);
        std::cout << "✓ Dependency update after a changed: " << cValue << std::endl;
        
        b.setValue(25);
        cValue = c.value();
        assert(cValue == 40);
        std::cout << "✓ Dependency update after b changed: " << cValue << std::endl;
        
        // 测试复杂依赖链：d = c * 2
        Property<int> d;
        d.setBinding(makePropertyBinding([&]() {
            return c.value() * 2;
        }));
        
        int dValue = d.value();
        assert(dValue == 80); // (15 + 25) * 2
        std::cout << "✓ Complex dependency chain: " << dValue << std::endl;
        
        // 测试传播更新
        a.setValue(20);
        dValue = d.value();
        assert(dValue == 90); // (20 + 25) * 2
        std::cout << "✓ Dependency propagation: " << dValue << std::endl;
        
    } catch (const std::exception& e) {
        std::cerr << "Exception in dependency tracking test: " << e.what() << std::endl;
        allPassed = false;
    } catch (...) {
        std::cerr << "Unknown exception in dependency tracking test" << std::endl;
        allPassed = false;
    }
    
    printTestResult("Property Dependency Tracking", allPassed);
}

// ==================================================================================
// 主测试函数
// ==================================================================================

int main() {
    std::cout << "=== Property System Core API Test Suite ===" << std::endl;
    std::cout << "Testing Property system core functionality..." << std::endl;
    
    try {
        // 执行所有核心API测试
        testPropertyValueOperations();
        testPropertyBindingState();
        testPropertyBindingFunctionality();
        testPropertyObservers();
        testPropertyDependencyTracking();
        
        std::cout << "\n=== Test Summary ===" << std::endl;
        std::cout << "All core API tests completed!" << std::endl;
        std::cout << "Property System Version: " << getPropertySystemVersion() << std::endl;
        std::cout << "Build Info: " << getPropertySystemBuildInfo() << std::endl;
        
        return 0;
        
    } catch (const std::exception& e) {
        std::cerr << "\nFATAL ERROR: " << e.what() << std::endl;
        return 1;
    } catch (...) {
        std::cerr << "\nFATAL ERROR: Unknown exception" << std::endl;
        return 1;
    }
}
