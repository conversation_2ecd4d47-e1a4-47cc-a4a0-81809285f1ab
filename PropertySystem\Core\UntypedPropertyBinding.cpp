/**
 * @file Core/UntypedPropertyBinding.cpp
 * @brief UntypedPropertyBinding类的实现
 * 
 * 这个文件实现了UntypedPropertyBinding类的核心功能，
 * 包括绑定的创建、管理和执行。
 */

#include "UntypedPropertyBinding.h"
#include "PropertyBindingPrivate.h"
#include "PropertyBindingError.h"
#include "../Binding/BindingVTable.h"
#include <memory>

namespace PropertySystem::Core {

// ==================================================================================
// UntypedPropertyBinding实现
// ==================================================================================

UntypedPropertyBinding::UntypedPropertyBinding() = default;

UntypedPropertyBinding::UntypedPropertyBinding(const Binding::ModernBindingFunctionVTable *vtable, 
                                              void *function, 
                                              const PropertyBindingSourceLocation &location) {
    if (vtable && function) {
        // 创建绑定私有数据
        d_ = std::make_shared<Internal::PropertyBindingPrivate>(vtable, location);
        
        // 这里需要复制绑定函数到私有数据中
        // 具体实现取决于绑定函数的存储方式
        // 暂时简化处理
    }
}

UntypedPropertyBinding::UntypedPropertyBinding(const UntypedPropertyBinding &other) = default;

UntypedPropertyBinding::UntypedPropertyBinding(UntypedPropertyBinding &&other) noexcept = default;

UntypedPropertyBinding& UntypedPropertyBinding::operator=(const UntypedPropertyBinding &other) = default;

UntypedPropertyBinding& UntypedPropertyBinding::operator=(UntypedPropertyBinding &&other) noexcept = default;

UntypedPropertyBinding::~UntypedPropertyBinding() = default;

bool UntypedPropertyBinding::isNull() const {
    return !d_ || !d_->isValid();
}

bool UntypedPropertyBinding::isValid() const {
    return d_ && d_->isValid();
}

PropertyBindingError UntypedPropertyBinding::error() const {
    if (d_) {
        return d_->error();
    }
    return PropertyBindingError();
}

PropertyBindingSourceLocation UntypedPropertyBinding::location() const {
    if (d_) {
        return d_->location();
    }
    return PropertyBindingSourceLocation();
}

void UntypedPropertyBinding::swap(UntypedPropertyBinding &other) noexcept {
    d_.swap(other.d_);
}

void UntypedPropertyBinding::reset() {
    d_.reset();
}

bool UntypedPropertyBinding::operator==(const UntypedPropertyBinding &other) const {
    return d_ == other.d_;
}

UntypedPropertyBinding::UntypedPropertyBinding(Internal::PropertyBindingPrivate *priv) 
    : d_(priv, [](Internal::PropertyBindingPrivate* p) { 
        // 自定义删除器，如果需要的话
        delete p; 
    }) {
}

// ==================================================================================
// PropertyBinding模板类实现
// ==================================================================================

// 模板类的实现在头文件中

} // namespace PropertySystem::Core

// ==================================================================================
// 全局命名空间别名
// ==================================================================================

using PropertySystem::Core::UntypedPropertyBinding;
using PropertySystem::Core::PropertyBinding;
