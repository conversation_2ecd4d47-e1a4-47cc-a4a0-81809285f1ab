/**
 * @file Core/PropertyBindingError.h
 * @brief 属性绑定错误处理
 * 
 * 这个文件实现了Property系统的错误处理机制，
 * 提供绑定错误的分类、存储和报告功能。
 */

#ifndef PROPERTY_SYSTEM_CORE_PROPERTY_BINDING_ERROR_H
#define PROPERTY_SYSTEM_CORE_PROPERTY_BINDING_ERROR_H

#include "PropertyTypes.h"
#include <string>
#include <memory>

namespace PropertySystem::Core {

// ==================================================================================
// 属性绑定错误类
// ==================================================================================

/**
 * @brief 属性绑定错误类
 * 
 * 这个类用于表示和管理属性绑定过程中发生的错误，
 * 包括错误类型、描述信息和源代码位置。
 */
class PropertyBindingError {
public:
    // ==================================================================================
    // 错误类型枚举
    // ==================================================================================
    
    /**
     * @brief 绑定错误类型
     */
    enum Type {
        NoError,           ///< 无错误
        BindingLoop,       ///< 绑定循环
        EvaluationError,   ///< 求值错误
        TypeMismatch,      ///< 类型不匹配
        InvalidBinding,    ///< 无效绑定
        UnknownError       ///< 未知错误
    };

    // ==================================================================================
    // 构造和析构
    // ==================================================================================
    
    /**
     * @brief 默认构造函数（无错误）
     */
    PropertyBindingError();
    
    /**
     * @brief 构造函数
     * @param type 错误类型
     * @param description 错误描述
     */
    PropertyBindingError(Type type, const std::string& description = "");
    
    /**
     * @brief 拷贝构造函数
     * @param other 其他错误对象
     */
    PropertyBindingError(const PropertyBindingError &other);
    
    /**
     * @brief 移动构造函数
     * @param other 其他错误对象
     */
    PropertyBindingError(PropertyBindingError &&other) noexcept;
    
    /**
     * @brief 拷贝赋值操作符
     * @param other 其他错误对象
     * @return 当前对象的引用
     */
    PropertyBindingError &operator=(const PropertyBindingError &other);
    
    /**
     * @brief 移动赋值操作符
     * @param other 其他错误对象
     * @return 当前对象的引用
     */
    PropertyBindingError &operator=(PropertyBindingError &&other) noexcept;
    
    /**
     * @brief 析构函数
     */
    ~PropertyBindingError();

    // ==================================================================================
    // 错误信息访问
    // ==================================================================================
    
    /**
     * @brief 检查是否有错误
     * @return 如果有错误则返回true
     */
    bool hasError() const { return d_ != nullptr; }
    
    /**
     * @brief 获取错误类型
     * @return 错误类型
     */
    Type type() const;
    
    /**
     * @brief 获取错误描述
     * @return 错误描述字符串
     */
    std::string description() const;
    
    /**
     * @brief 获取错误的字符串表示
     * @return 错误的完整字符串描述
     */
    std::string toString() const;
    
    /**
     * @brief 检查错误是否为指定类型
     * @param errorType 要检查的错误类型
     * @return 如果是指定类型则返回true
     */
    bool isType(Type errorType) const {
        return type() == errorType;
    }

    // ==================================================================================
    // 比较操作符
    // ==================================================================================
    
    /**
     * @brief 相等比较操作符
     * @param other 其他错误对象
     * @return 如果相等则返回true
     */
    bool operator==(const PropertyBindingError &other) const;
    
    /**
     * @brief 不等比较操作符
     * @param other 其他错误对象
     * @return 如果不等则返回true
     */
    bool operator!=(const PropertyBindingError &other) const {
        return !(*this == other);
    }

    // ==================================================================================
    // 静态工厂方法
    // ==================================================================================
    
    /**
     * @brief 创建绑定循环错误
     * @param description 错误描述
     * @return 绑定循环错误对象
     */
    static PropertyBindingError createBindingLoopError(const std::string& description = "Binding loop detected");
    
    /**
     * @brief 创建求值错误
     * @param description 错误描述
     * @return 求值错误对象
     */
    static PropertyBindingError createEvaluationError(const std::string& description = "Binding evaluation failed");
    
    /**
     * @brief 创建类型不匹配错误
     * @param description 错误描述
     * @return 类型不匹配错误对象
     */
    static PropertyBindingError createTypeMismatchError(const std::string& description = "Type mismatch in binding");
    
    /**
     * @brief 创建无效绑定错误
     * @param description 错误描述
     * @return 无效绑定错误对象
     */
    static PropertyBindingError createInvalidBindingError(const std::string& description = "Invalid binding");

    // ==================================================================================
    // 错误类型字符串转换
    // ==================================================================================
    
    /**
     * @brief 将错误类型转换为字符串
     * @param type 错误类型
     * @return 错误类型的字符串表示
     */
    static std::string typeToString(Type type);
    
    /**
     * @brief 从字符串解析错误类型
     * @param typeString 错误类型字符串
     * @return 错误类型
     */
    static Type typeFromString(const std::string& typeString);

private:
    // ==================================================================================
    // 私有实现类
    // ==================================================================================
    
    /**
     * @brief 错误数据私有实现
     */
    struct ErrorData {
        Type type = NoError;
        std::string description;
        
        ErrorData() = default;
        ErrorData(Type t, const std::string& desc) : type(t), description(desc) {}
    };
    
    // ==================================================================================
    // 私有成员变量
    // ==================================================================================
    
    std::shared_ptr<ErrorData> d_;  ///< 错误数据的共享指针

    // ==================================================================================
    // 私有辅助方法
    // ==================================================================================
    
    /**
     * @brief 确保错误数据存在
     */
    void ensureErrorData();
};

// ==================================================================================
// 内联实现
// ==================================================================================

inline PropertyBindingError::Type PropertyBindingError::type() const {
    return d_ ? d_->type : NoError;
}

inline std::string PropertyBindingError::description() const {
    return d_ ? d_->description : std::string();
}

inline bool PropertyBindingError::operator==(const PropertyBindingError &other) const {
    if (d_ == other.d_) {
        return true;
    }
    if (!d_ || !other.d_) {
        return false;
    }
    return d_->type == other.d_->type && d_->description == other.d_->description;
}

inline PropertyBindingError PropertyBindingError::createBindingLoopError(const std::string& description) {
    return PropertyBindingError(BindingLoop, description);
}

inline PropertyBindingError PropertyBindingError::createEvaluationError(const std::string& description) {
    return PropertyBindingError(EvaluationError, description);
}

inline PropertyBindingError PropertyBindingError::createTypeMismatchError(const std::string& description) {
    return PropertyBindingError(TypeMismatch, description);
}

inline PropertyBindingError PropertyBindingError::createInvalidBindingError(const std::string& description) {
    return PropertyBindingError(InvalidBinding, description);
}

// ==================================================================================
// 错误类型字符串转换实现
// ==================================================================================

inline std::string PropertyBindingError::typeToString(Type type) {
    switch (type) {
        case NoError: return "NoError";
        case BindingLoop: return "BindingLoop";
        case EvaluationError: return "EvaluationError";
        case TypeMismatch: return "TypeMismatch";
        case InvalidBinding: return "InvalidBinding";
        case UnknownError: return "UnknownError";
        default: return "UnknownError";
    }
}

inline PropertyBindingError::Type PropertyBindingError::typeFromString(const std::string& typeString) {
    if (typeString == "NoError") return NoError;
    if (typeString == "BindingLoop") return BindingLoop;
    if (typeString == "EvaluationError") return EvaluationError;
    if (typeString == "TypeMismatch") return TypeMismatch;
    if (typeString == "InvalidBinding") return InvalidBinding;
    return UnknownError;
}

} // namespace PropertySystem::Core

// ==================================================================================
// 全局命名空间别名
// ==================================================================================

using PropertySystem::Core::PropertyBindingError;

#endif // PROPERTY_SYSTEM_CORE_PROPERTY_BINDING_ERROR_H
