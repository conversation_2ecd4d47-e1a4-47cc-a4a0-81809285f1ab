/**
 * @file test_basic_api.cpp
 * @brief Property系统基础API测试
 * 
 * 这个文件测试Property系统的基础功能，
 * 避免复杂的绑定系统，专注于核心API。
 */

#include "PropertySystem/Core/PropertyData.h"
#include "PropertySystem/Core/PropertyTypes.h"
#include "PropertySystem/Binding/BindingLocation.h"
#include "PropertySystem/Core/PropertyBindingError.h"
#include "PropertySystem/Core/PropertyUpdateGroup.h"
#include <iostream>
#include <string>
#include <cassert>

using namespace PropertySystem::Core;

// ==================================================================================
// 测试辅助函数
// ==================================================================================

void printTestHeader(const std::string& testName) {
    std::cout << "\n=== " << testName << " ===" << std::endl;
}

void printTestResult(const std::string& testName, bool passed) {
    std::cout << (passed ? "✓ " : "✗ ") << testName << (passed ? " PASSED" : " FAILED") << std::endl;
}

// ==================================================================================
// 1. PropertyData基础功能测试
// ==================================================================================

void testPropertyDataBasics() {
    printTestHeader("PropertyData Basics Test");
    
    bool allPassed = true;
    
    try {
        // 测试整数PropertyData
        PropertyData<int> intData(42);
        assert(intData.valueBypassingBindings() == 42);
        std::cout << "✓ PropertyData<int> initial value: " << intData.valueBypassingBindings() << std::endl;
        
        // 测试setValue
        intData.setValueBypassingBindings(100);
        assert(intData.valueBypassingBindings() == 100);
        std::cout << "✓ PropertyData<int> setValue: " << intData.valueBypassingBindings() << std::endl;
        
        // 测试隐式转换
        int value = intData;
        assert(value == 100);
        std::cout << "✓ PropertyData<int> implicit conversion: " << value << std::endl;
        
        // 测试赋值操作符
        intData = 200;
        assert(intData.valueBypassingBindings() == 200);
        std::cout << "✓ PropertyData<int> assignment: " << intData.valueBypassingBindings() << std::endl;
        
        // 测试字符串PropertyData
        PropertyData<std::string> stringData;
        stringData.setValueBypassingBindings("Hello");
        assert(stringData.valueBypassingBindings() == "Hello");
        std::cout << "✓ PropertyData<string> setValue: " << stringData.valueBypassingBindings() << std::endl;
        
        stringData = std::string("World");
        assert(stringData.valueBypassingBindings() == "World");
        std::cout << "✓ PropertyData<string> assignment: " << stringData.valueBypassingBindings() << std::endl;
        
        // 测试比较操作
        PropertyData<int> intData2(200);
        assert(intData == intData2);
        std::cout << "✓ PropertyData equality comparison works" << std::endl;
        
        intData2.setValueBypassingBindings(300);
        assert(intData != intData2);
        std::cout << "✓ PropertyData inequality comparison works" << std::endl;
        
    } catch (const std::exception& e) {
        std::cerr << "Exception in PropertyData basics test: " << e.what() << std::endl;
        allPassed = false;
    } catch (...) {
        std::cerr << "Unknown exception in PropertyData basics test" << std::endl;
        allPassed = false;
    }
    
    printTestResult("PropertyData Basics", allPassed);
}

// ==================================================================================
// 2. PropertyBindingError测试
// ==================================================================================

void testPropertyBindingError() {
    printTestHeader("PropertyBindingError Test");
    
    bool allPassed = true;
    
    try {
        // 测试默认构造（无错误）
        PropertyBindingError noError;
        assert(!noError.hasError());
        assert(noError.type() == PropertyBindingError::NoError);
        std::cout << "✓ Default PropertyBindingError has no error" << std::endl;
        
        // 测试创建绑定循环错误
        PropertyBindingError bindingLoop = PropertyBindingError::createBindingLoopError("Test loop");
        assert(bindingLoop.hasError());
        assert(bindingLoop.type() == PropertyBindingError::BindingLoop);
        assert(bindingLoop.description() == "Test loop");
        std::cout << "✓ BindingLoop error created: " << bindingLoop.toString() << std::endl;
        
        // 测试创建求值错误
        PropertyBindingError evalError = PropertyBindingError::createEvaluationError("Test evaluation");
        assert(evalError.hasError());
        assert(evalError.type() == PropertyBindingError::EvaluationError);
        assert(evalError.description() == "Test evaluation");
        std::cout << "✓ EvaluationError created: " << evalError.toString() << std::endl;
        
        // 测试类型不匹配错误
        PropertyBindingError typeError = PropertyBindingError::createTypeMismatchError("Test type");
        assert(typeError.hasError());
        assert(typeError.type() == PropertyBindingError::TypeMismatch);
        std::cout << "✓ TypeMismatch error created: " << typeError.toString() << std::endl;
        
        // 测试无效绑定错误
        PropertyBindingError invalidError = PropertyBindingError::createInvalidBindingError("Test invalid");
        assert(invalidError.hasError());
        assert(invalidError.type() == PropertyBindingError::InvalidBinding);
        std::cout << "✓ InvalidBinding error created: " << invalidError.toString() << std::endl;
        
        // 测试错误类型字符串转换
        std::string typeStr = PropertyBindingError::typeToString(PropertyBindingError::BindingLoop);
        assert(typeStr == "BindingLoop");
        std::cout << "✓ Type to string conversion: " << typeStr << std::endl;
        
        PropertyBindingError::Type parsedType = PropertyBindingError::typeFromString("EvaluationError");
        assert(parsedType == PropertyBindingError::EvaluationError);
        std::cout << "✓ String to type conversion works" << std::endl;
        
        // 测试错误比较
        PropertyBindingError error1 = PropertyBindingError::createBindingLoopError("Same");
        PropertyBindingError error2 = PropertyBindingError::createBindingLoopError("Same");
        PropertyBindingError error3 = PropertyBindingError::createBindingLoopError("Different");
        
        assert(error1 == error2);
        assert(error1 != error3);
        std::cout << "✓ Error comparison works correctly" << std::endl;
        
    } catch (const std::exception& e) {
        std::cerr << "Exception in PropertyBindingError test: " << e.what() << std::endl;
        allPassed = false;
    } catch (...) {
        std::cerr << "Unknown exception in PropertyBindingError test" << std::endl;
        allPassed = false;
    }
    
    printTestResult("PropertyBindingError", allPassed);
}

// ==================================================================================
// 3. PropertyBindingSourceLocation测试
// ==================================================================================

void testPropertyBindingSourceLocation() {
    printTestHeader("PropertyBindingSourceLocation Test");
    
    bool allPassed = true;
    
    try {
        // 测试默认构造
        PropertyBindingSourceLocation defaultLoc;
        assert(!defaultLoc.isValid());
        std::cout << "✓ Default location is invalid" << std::endl;
        
        // 测试手动构造
        PropertyBindingSourceLocation manualLoc("test.cpp", "testFunction", 42, 10);
        assert(manualLoc.isValid());
        assert(manualLoc.line == 42);
        assert(manualLoc.column == 10);
        std::cout << "✓ Manual location: " << manualLoc.toString() << std::endl;
        
        // 测试文件名提取
        PropertyBindingSourceLocation pathLoc("/path/to/file.cpp", "func", 100, 5);
        std::cout << "✓ File name extraction: " << pathLoc.file() << std::endl;
        
        // 测试比较操作
        PropertyBindingSourceLocation loc1("file.cpp", "func", 10, 5);
        PropertyBindingSourceLocation loc2("file.cpp", "func", 10, 5);
        PropertyBindingSourceLocation loc3("file.cpp", "func", 20, 5);
        
        assert(loc1 == loc2);
        assert(loc1 != loc3);
        std::cout << "✓ Location comparison works" << std::endl;
        
        // 测试默认绑定位置宏
        PropertyBindingSourceLocation currentLoc = PROPERTY_DEFAULT_BINDING_LOCATION;
        std::cout << "✓ Default binding location: " << currentLoc.toString() << std::endl;
        
    } catch (const std::exception& e) {
        std::cerr << "Exception in PropertyBindingSourceLocation test: " << e.what() << std::endl;
        allPassed = false;
    } catch (...) {
        std::cerr << "Unknown exception in PropertyBindingSourceLocation test" << std::endl;
        allPassed = false;
    }
    
    printTestResult("PropertyBindingSourceLocation", allPassed);
}

// ==================================================================================
// 4. PropertyUpdateGroup测试
// ==================================================================================

void testPropertyUpdateGroup() {
    printTestHeader("PropertyUpdateGroup Test");
    
    bool allPassed = true;
    
    try {
        // 测试初始状态
        assert(!isInPropertyUpdateGroup());
        assert(getPropertyUpdateGroupDepth() == 0);
        std::cout << "✓ Initial state: not in update group" << std::endl;
        
        // 测试手动更新组
        beginPropertyUpdateGroup();
        assert(isInPropertyUpdateGroup());
        assert(getPropertyUpdateGroupDepth() == 1);
        std::cout << "✓ After beginPropertyUpdateGroup: depth = " << getPropertyUpdateGroupDepth() << std::endl;
        
        // 测试嵌套更新组
        beginPropertyUpdateGroup();
        assert(getPropertyUpdateGroupDepth() == 2);
        std::cout << "✓ Nested update group: depth = " << getPropertyUpdateGroupDepth() << std::endl;
        
        endPropertyUpdateGroup();
        assert(getPropertyUpdateGroupDepth() == 1);
        std::cout << "✓ After first endPropertyUpdateGroup: depth = " << getPropertyUpdateGroupDepth() << std::endl;
        
        endPropertyUpdateGroup();
        assert(!isInPropertyUpdateGroup());
        assert(getPropertyUpdateGroupDepth() == 0);
        std::cout << "✓ After second endPropertyUpdateGroup: depth = " << getPropertyUpdateGroupDepth() << std::endl;
        
        // 测试RAII更新组
        {
            ScopedPropertyUpdateGroup group;
            assert(isInPropertyUpdateGroup());
            assert(getPropertyUpdateGroupDepth() == 1);
            std::cout << "✓ ScopedPropertyUpdateGroup: in group" << std::endl;
        }
        assert(!isInPropertyUpdateGroup());
        assert(getPropertyUpdateGroupDepth() == 0);
        std::cout << "✓ After ScopedPropertyUpdateGroup destroyed: not in group" << std::endl;
        
        // 测试便利函数
        bool executed = false;
        withUpdateGroup([&]() {
            assert(isInPropertyUpdateGroup());
            executed = true;
            std::cout << "✓ withUpdateGroup: inside lambda" << std::endl;
        });
        assert(executed);
        assert(!isInPropertyUpdateGroup());
        std::cout << "✓ withUpdateGroup: after lambda" << std::endl;
        
    } catch (const std::exception& e) {
        std::cerr << "Exception in PropertyUpdateGroup test: " << e.what() << std::endl;
        allPassed = false;
    } catch (...) {
        std::cerr << "Unknown exception in PropertyUpdateGroup test" << std::endl;
        allPassed = false;
    }
    
    printTestResult("PropertyUpdateGroup", allPassed);
}

// ==================================================================================
// 主测试函数
// ==================================================================================

int main() {
    std::cout << "=== Property System Basic API Test Suite ===" << std::endl;
    std::cout << "Testing basic Property system functionality..." << std::endl;
    
    try {
        // 执行基础API测试
        testPropertyDataBasics();
        testPropertyBindingError();
        testPropertyBindingSourceLocation();
        testPropertyUpdateGroup();
        
        std::cout << "\n=== Test Summary ===" << std::endl;
        std::cout << "All basic API tests completed!" << std::endl;
        
        return 0;
        
    } catch (const std::exception& e) {
        std::cerr << "\nFATAL ERROR: " << e.what() << std::endl;
        return 1;
    } catch (...) {
        std::cerr << "\nFATAL ERROR: Unknown exception" << std::endl;
        return 1;
    }
}
