/**
 * @file Core/PropertyBindingPrivate.h
 * @brief 属性绑定私有实现
 * 
 * 这个文件实现了Property系统的绑定私有数据，
 * 负责绑定的执行、依赖管理和错误处理。
 */

#ifndef PROPERTY_SYSTEM_CORE_PROPERTY_BINDING_PRIVATE_H
#define PROPERTY_SYSTEM_CORE_PROPERTY_BINDING_PRIVATE_H

#include "PropertyTypes.h"
#include "../Binding/BindingLocation.h"
#include "../Binding/BindingVTable.h"
#include <memory>
#include <atomic>
#include <vector>
#include <array>

namespace PropertySystem::Core::Internal {

// ==================================================================================
// 前向声明
// ==================================================================================

class PropertyObserver;
struct PropertyObserverPointer;
struct BindingEvaluationState;
class PropertyBindingError;
class UntypedPropertyData;
class PropertyBindingData;

// ==================================================================================
// 引用计数基类
// ==================================================================================

/**
 * @brief 引用计数基类
 * 
 * 提供线程安全的引用计数功能。
 */
class RefCounted {
public:
    /**
     * @brief 增加引用计数
     */
    void addRef() {
        ref_count_.fetch_add(1, std::memory_order_relaxed);
    }
    
    /**
     * @brief 减少引用计数
     * @return 如果引用计数不为0则返回true
     */
    bool deref() {
        return ref_count_.fetch_sub(1, std::memory_order_acq_rel) != 1;
    }
    
    /**
     * @brief 获取当前引用计数
     * @return 引用计数
     */
    int refCount() const {
        return ref_count_.load(std::memory_order_relaxed);
    }

protected:
    RefCounted() = default;
    virtual ~RefCounted() = default;

private:
    std::atomic<int> ref_count_{0};
};

// ==================================================================================
// 属性绑定私有实现类
// ==================================================================================

/**
 * @brief 属性绑定私有实现类
 * 
 * 这个类是绑定系统的核心，负责：
 * - 绑定函数的存储和执行
 * - 依赖关系的管理
 * - 观察者的通知
 * - 错误处理和循环检测
 */
class PropertyBindingPrivate : public RefCounted {
public:
    // ==================================================================================
    // 类型定义
    // ==================================================================================
    
    using ObserverArray = std::array<PropertyObserver, 4>;  ///< 内联观察者数组
    using HeapObservers = std::vector<PropertyObserver>;    ///< 堆观察者容器

    // ==================================================================================
    // 构造和析构
    // ==================================================================================
    
    /**
     * @brief 构造函数
     * @param vtable 绑定函数虚表
     * @param location 源代码位置信息
     */
    PropertyBindingPrivate(const Binding::ModernBindingFunctionVTable *vtable,
                          const PropertyBindingSourceLocation &location);
    
    /**
     * @brief 析构函数
     */
    ~PropertyBindingPrivate();

    // ==================================================================================
    // 绑定执行
    // ==================================================================================
    
    /**
     * @brief 递归评估绑定
     * @param bindingObservers 绑定观察者列表
     * @param status 绑定状态
     * @return 如果值发生变化则返回true
     */
    bool evaluateRecursive(std::vector<PropertyObserver*> &bindingObservers, 
                          BindingEvaluationState *status = nullptr);
    
    /**
     * @brief 内联递归评估绑定
     * @param bindingObservers 绑定观察者列表
     * @param status 绑定状态
     * @return 如果值发生变化则返回true
     */
    bool evaluateRecursive_inline(std::vector<PropertyObserver*> &bindingObservers, 
                                 BindingEvaluationState *status);
    
    /**
     * @brief 非递归通知观察者
     * @param bindingObservers 绑定观察者列表
     */
    void notifyNonRecursive(const std::vector<PropertyObserver*> &bindingObservers);
    
    /**
     * @brief 非递归通知观察者（无参数版本）
     * @return 通知状态
     */
    enum NotificationState : bool { Delayed, Sent };
    NotificationState notifyNonRecursive();

    // ==================================================================================
    // 状态管理
    // ==================================================================================
    
    /**
     * @brief 检查是否正在更新
     * @return 如果正在更新则返回true
     */
    bool isUpdating() const { return updating_; }
    
    /**
     * @brief 设置粘性标志
     * @param sticky 是否为粘性绑定
     */
    void setSticky(bool sticky = true) { sticky_ = sticky; }
    
    /**
     * @brief 检查是否为粘性绑定
     * @return 如果是粘性绑定则返回true
     */
    bool isSticky() const { return sticky_; }
    
    /**
     * @brief 安排通知
     */
    void scheduleNotify() { pending_notify_ = true; }
    
    /**
     * @brief 检查是否有待处理的通知
     * @return 如果有待处理的通知则返回true
     */
    bool hasPendingNotify() const { return pending_notify_; }

    // ==================================================================================
    // 属性和观察者管理
    // ==================================================================================
    
    /**
     * @brief 设置关联的属性
     * @param propertyPtr 属性数据指针
     */
    void setProperty(UntypedPropertyData *propertyPtr) { 
        property_data_ptr_ = propertyPtr; 
    }
    
    /**
     * @brief 获取关联的属性
     * @return 属性数据指针
     */
    UntypedPropertyData* property() const { 
        return property_data_ptr_; 
    }
    
    /**
     * @brief 前置观察者
     * @param observer 观察者指针
     */
    void prependObserver(PropertyObserver *observer);
    
    /**
     * @brief 获取并移除所有观察者
     * @return 第一个观察者指针
     */
    PropertyObserver* takeObservers();
    
    /**
     * @brief 清理依赖观察者
     */
    void clearDependencyObservers();
    
    /**
     * @brief 分配依赖观察者
     * @return 观察者指针
     */
    PropertyObserver* allocateDependencyObserver();

    // ==================================================================================
    // 错误处理
    // ==================================================================================
    
    /**
     * @brief 获取绑定错误
     * @return 绑定错误对象
     */
    PropertyBindingError bindingError() const;
    
    /**
     * @brief 设置错误
     * @param error 错误对象
     */
    void setError(PropertyBindingError &&error);
    
    /**
     * @brief 获取源代码位置
     * @return 源代码位置信息
     */
    PropertyBindingSourceLocation sourceLocation() const { return location_; }

    // ==================================================================================
    // 静态方法
    // ==================================================================================
    
    /**
     * @brief 获取当前正在评估的绑定
     * @return 当前绑定的私有数据指针
     */
    static PropertyBindingPrivate* currentlyEvaluatingBinding();
    
    /**
     * @brief 销毁并释放内存
     * @param priv 私有数据指针
     */
    static void destroyAndFreeMemory(PropertyBindingPrivate *priv);
    
    /**
     * @brief 获取确保对齐的大小
     * @return 对齐后的大小
     */
    static constexpr size_t getSizeEnsuringAlignment() {
        constexpr auto align = alignof(std::max_align_t) - 1;
        constexpr size_t sizeEnsuringAlignment = (sizeof(PropertyBindingPrivate) + align) & ~align;
        static_assert(sizeEnsuringAlignment % alignof(std::max_align_t) == 0,
                     "Required for placement new'ing the function behind it.");
        return sizeEnsuringAlignment;
    }

    // ==================================================================================
    // 内部接口
    // ==================================================================================
    
    /**
     * @brief 从属性解除关联
     */
    void detachFromProperty();
    
    /**
     * @brief 解除链接并减少引用
     */
    void unlinkAndDeref();

private:
    // ==================================================================================
    // 私有成员变量
    // ==================================================================================
    
    // 状态标志
    bool updating_ = false;              ///< 是否正在更新
    bool pending_notify_ = false;        ///< 是否有待处理的通知
    bool sticky_ = false;                ///< 是否为粘性绑定
    
    // 绑定函数相关
    const Binding::ModernBindingFunctionVTable *vtable_;  ///< 绑定函数虚表
    PropertyBindingSourceLocation location_;              ///< 源代码位置信息
    
    // 属性和观察者
    UntypedPropertyData *property_data_ptr_ = nullptr;    ///< 关联的属性数据
    PropertyObserver *first_observer_ = nullptr;          ///< 第一个观察者
    
    // 依赖观察者管理
    ObserverArray inline_dependency_observers_;           ///< 内联依赖观察者数组
    std::unique_ptr<HeapObservers> heap_observers_;       ///< 堆依赖观察者
    size_t dependency_observer_count_ = 0;                ///< 依赖观察者数量
    
    // 错误处理
    std::unique_ptr<PropertyBindingError> error_;         ///< 绑定错误

    // ==================================================================================
    // 私有辅助方法
    // ==================================================================================
    
    /**
     * @brief 分配依赖观察者（慢路径）
     * @return 观察者指针
     */
    PropertyObserver* allocateDependencyObserver_slow();
    
    /**
     * @brief 检查是否有自定义虚表
     * @return 如果有自定义虚表则返回true
     */
    bool hasCustomVTable() const { return vtable_->size == 0; }

    // ==================================================================================
    // 友元声明
    // ==================================================================================
    
    friend class PropertyBindingData;
    friend struct PropertyObserverPointer;
    friend class UntypedPropertyBinding;
    template<typename T> friend class Property;
};

// ==================================================================================
// 智能指针类型定义
// ==================================================================================

/**
 * @brief PropertyBindingPrivate的智能指针类型
 */
using PropertyBindingPrivatePtr = std::shared_ptr<PropertyBindingPrivate>;

} // namespace PropertySystem::Core::Internal

// ==================================================================================
// 全局命名空间别名
// ==================================================================================

using PropertySystem::Core::Internal::PropertyBindingPrivate;
using PropertySystem::Core::Internal::PropertyBindingPrivatePtr;
using PropertySystem::Core::Internal::RefCounted;

#endif // PROPERTY_SYSTEM_CORE_PROPERTY_BINDING_PRIVATE_H
