﻿#pragma once
#ifndef STANDARDPATHS_H
#define STANDARDPATHS_H

#include <string>
#include <vector>
/**
 * @brief The StandardPaths class provides methods for accessing standard system paths.
 *
 * This class provides a platform-independent way to access standard system paths
 * such as the user's home directory, documents, downloads, etc. It also provides
 * methods for accessing application-specific paths.
 */
class StandardPaths
{
public:

    /**
     * @brief Enum for legacy compatibility with the old API
     */
    enum class DirectoryType {
        Home,
        Documents,
        Downloads,
        Music,
        Pictures,
        Videos,
        Desktop,
        AppData,
        Temp,
        Current,
        Cache,
        Config
    };

    // Deleted copy constructor and assignment operator
    StandardPaths(const StandardPaths&) = delete;
    StandardPaths& operator=(const StandardPaths&) = delete;

    /**
     * @brief Returns the path for the specified directory type.
     * @param type The directory type.
     * @return The path for the specified directory type.
     */
    static std::string getPath(DirectoryType type);

    /**
     * @brief Returns the standard locations for the specified directory type.
     * @param type The directory type.
     * @return A vector of paths for the specified directory type.
     */
    static std::vector<std::string> standardLocations(DirectoryType type);

private:
    StandardPaths() = default;
    static std::string formatPath(const std::string& path);
};

#endif // STANDARDPATHS_H
