#ifndef BUFFER_H
#define BUFFER_H

#include "iodevice.h"
#include <vector>
#include <cstdint>

namespace filesystem {

/**
 * @brief The Buffer class provides a memory buffer for I/O operations.
 * 
 * Buffer is an IODevice that operates on a memory buffer, allowing
 * reading from and writing to memory as if it were a file.
 */
class Buffer : public IODevice {
public:
    /**
     * @brief Constructs an empty buffer.
     */
    Buffer();

    /**
     * @brief Constructs a buffer with the given data.
     * @param data The initial data for the buffer.
     */
    explicit Buffer(const std::vector<char>& data);

    /**
     * @brief Constructs a buffer with the given data.
     * @param data The initial data for the buffer.
     */
    explicit Buffer(std::vector<char>&& data);

    /**
     * @brief Constructs a buffer with the given data.
     * @param data The initial data for the buffer.
     * @param size The size of the data.
     */
    Buffer(const char* data, int64_t size);

    /**
     * @brief Constructs a buffer with the given string.
     * @param data The initial data for the buffer.
     */
    explicit Buffer(const std::string& data);

    /**
     * @brief Destructor.
     */
    ~Buffer() override;

    /**
     * @brief Opens the buffer with the specified mode.
     * @param mode The open mode.
     * @return True if the buffer was opened successfully, false otherwise.
     */
    bool open(OpenMode mode) override;

    /**
     * @brief Closes the buffer.
     */
    void close() override;

    /**
     * @brief Returns whether the buffer is open.
     * @return True if the buffer is open, false otherwise.
     */
    bool isOpen() const override;

    /**
     * @brief Returns the open mode of the buffer.
     * @return The open mode.
     */
    OpenMode openMode() const override;

    /**
     * @brief Returns the size of the buffer.
     * @return The size in bytes.
     */
    int64_t size() const override;

    /**
     * @brief Returns the current position in the buffer.
     * @return The current position.
     */
    int64_t pos() const override;

    /**
     * @brief Sets the current position in the buffer.
     * @param pos The new position.
     * @param origin The origin from which to seek.
     * @return The new position, or -1 if an error occurred.
     */
    int64_t seek(int64_t pos, SeekOrigin origin = SeekOrigin::Begin) override;

    /**
     * @brief Returns whether the buffer is at the end.
     * @return True if the buffer is at the end, false otherwise.
     */
    bool atEnd() const override;

    /**
     * @brief Reads data from the buffer.
     * @param data The buffer to read into.
     * @param maxSize The maximum number of bytes to read.
     * @return The number of bytes read, or -1 if an error occurred.
     */
    int64_t read(char* data, int64_t maxSize) override;

    /**
     * @brief Writes data to the buffer.
     * @param data The data to write.
     * @param size The number of bytes to write.
     * @return The number of bytes written, or -1 if an error occurred.
     */
    int64_t write(const char* data, int64_t size) override;

    /**
     * @brief Flushes any buffered data.
     * @return Always returns true for Buffer.
     */
    bool flush() override;

    /**
     * @brief Returns the error string for the last error.
     * @return The error string, or an empty string if no error occurred.
     */
    std::string errorString() const override;

    /**
     * @brief Returns the buffer data.
     * @return The buffer data.
     */
    const std::vector<char>& data() const;

    /**
     * @brief Sets the buffer data.
     * @param data The new buffer data.
     */
    void setData(const std::vector<char>& data);

    /**
     * @brief Sets the buffer data.
     * @param data The new buffer data.
     */
    void setData(std::vector<char>&& data);

    /**
     * @brief Sets the buffer data.
     * @param data The new buffer data.
     * @param size The size of the data.
     */
    void setData(const char* data, int64_t size);

    /**
     * @brief Sets the buffer data.
     * @param data The new buffer data.
     */
    void setData(const std::string& data);

    /**
     * @brief Clears the buffer.
     */
    void clear();

    /**
     * @brief Resizes the buffer.
     * @param size The new size.
     */
    void resize(int64_t size);

    /**
     * @brief Reserves space in the buffer.
     * @param size The size to reserve.
     */
    void reserve(int64_t size);

private:
    std::vector<char> m_buffer;
    int64_t m_pos;
    std::string m_errorString;
};

} // namespace filesystem

#endif // BUFFER_H
