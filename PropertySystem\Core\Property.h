/**
 * @file Core/Property.h
 * @brief Property核心类实现
 * 
 * 这个文件实现了Property系统的核心Property模板类，
 * 提供完整的属性值管理、绑定系统和观察者模式支持。
 */

#ifndef PROPERTY_SYSTEM_CORE_PROPERTY_H
#define PROPERTY_SYSTEM_CORE_PROPERTY_H

#include "PropertyData.h"
#include "PropertyTypes.h"
#include "../Binding/BindingLocation.h"
#include "UntypedPropertyBinding.h"
#include "PropertyBindingData.h"
#include "PropertyBindingPrivate.h"
#include <functional>
#include <memory>

namespace PropertySystem::Core {

// ==================================================================================
// 前向声明
// ==================================================================================

namespace Internal {
    class PropertyBindingData;
    class PropertyBindingPrivate;
    template<typename T> class PropertyBinding;
}

// 核心类前向声明
class PropertyBindingData;
class PropertyBindingPrivate;
template<typename T> class PropertyBinding;

// ==================================================================================
// Property核心模板类
// ==================================================================================

/**
 * @brief Property核心模板类
 * @tparam T 属性值类型
 * 
 * 这个类是Property系统的核心，提供完整的属性功能：
 * - 类型安全的值存储和访问
 * - 属性绑定系统支持
 * - 观察者模式和变化通知
 * - 自动依赖追踪
 */
template<typename T>
requires PropertyType<T>
class Property : public PropertyData<T> {
public:
    // ==================================================================================
    // 类型定义
    // ==================================================================================
    
    using value_type = typename PropertyData<T>::value_type;
    using parameter_type = typename PropertyData<T>::parameter_type;
    using rvalue_ref = typename PropertyData<T>::rvalue_ref;
    
    // ==================================================================================
    // 构造函数
    // ==================================================================================
    
    /**
     * @brief 默认构造函数
     */
    Property() = default;
    
    /**
     * @brief 从初始值构造
     * @param initialValue 初始值
     */
    explicit Property(parameter_type initialValue) 
        : PropertyData<T>(initialValue) {}
    
    /**
     * @brief 从右值构造
     * @param initialValue 初始值（右值引用）
     */
    explicit Property(rvalue_ref initialValue) requires PropertySystem::Core::UseReferenceSemantics<T>
        : PropertyData<T>(std::move(initialValue)) {}
    
    /**
     * @brief 从绑定构造
     * @param binding 属性绑定对象
     */
    template<typename BindingType>
    explicit Property(const BindingType &binding) : Property() {
        setBinding(binding);
    }
    
    /**
     * @brief 从可调用对象构造绑定
     * @tparam Functor 可调用对象类型
     * @param f 可调用对象
     * @param location 源代码位置信息
     */
    template<typename Functor>
    requires PropertyBindingCallable<Functor, T>
    explicit Property(Functor &&f, 
                     const PropertyBindingSourceLocation &location = PROPERTY_DEFAULT_BINDING_LOCATION)
        : Property() {
        setBinding(std::forward<Functor>(f), location);
    }

    // ==================================================================================
    // 值访问方法
    // ==================================================================================
    
    /**
     * @brief 获取属性值
     * @return 属性值
     * 
     * 这个方法会自动注册当前正在评估的绑定为依赖，
     * 并在必要时触发绑定的重新评估。
     */
    parameter_type value() const {
        registerWithCurrentlyEvaluatingBinding();
        return this->val;
    }
    
    /**
     * @brief 箭头操作符重载
     * @return 属性值的指针或引用
     */
    auto operator->() const {
        if constexpr (std::is_pointer_v<T>) {
            value(); // 确保依赖注册
            return this->val;
        } else if constexpr (requires { this->val.operator->(); }) {
            return value();
        } else {
            static_assert(std::is_pointer_v<T> || requires { this->val.operator->(); },
                         "Type must be pointer or have operator->");
        }
    }
    
    /**
     * @brief 解引用操作符重载
     * @return 属性值
     */
    parameter_type operator*() const {
        return value();
    }
    
    /**
     * @brief 隐式类型转换操作符
     * @return 属性值
     */
    operator parameter_type() const {
        return value();
    }

    // ==================================================================================
    // 值设置方法
    // ==================================================================================
    
    /**
     * @brief 设置属性值
     * @param newValue 新值
     */
    void setValue(parameter_type newValue) {
        removeBinding();
        if (isEqual(newValue)) {
            return;
        }
        this->val = newValue;
        notifyObservers();
    }
    
    /**
     * @brief 设置属性值（右值版本）
     * @param newValue 新值（右值引用）
     */
    void setValue(rvalue_ref newValue) requires PropertySystem::Core::UseReferenceSemantics<T> && (!std::is_same_v<T, std::remove_cvref_t<rvalue_ref>>) {
        removeBinding();
        if (isEqual(newValue)) {
            return;
        }
        this->val = std::move(newValue);
        notifyObservers();
    }
    
    /**
     * @brief 赋值操作符
     * @param newValue 新值
     * @return 当前对象的引用
     */
    Property<T> &operator=(parameter_type newValue) {
        setValue(newValue);
        return *this;
    }
    
    /**
     * @brief 赋值操作符（右值版本）
     * @param newValue 新值（右值引用）
     * @return 当前对象的引用
     */
    Property<T> &operator=(rvalue_ref newValue) requires PropertySystem::Core::UseReferenceSemantics<T> {
        setValue(std::move(newValue));
        return *this;
    }

    // ==================================================================================
    // 绑定管理方法
    // ==================================================================================
    
    /**
     * @brief 设置属性绑定
     * @tparam BindingType 绑定类型
     * @param newBinding 新的绑定对象
     * @return 之前的绑定对象
     */
    template<typename BindingType>
    auto setBinding(const BindingType &newBinding) {
        return setBindingImpl(newBinding);
    }
    
    /**
     * @brief 从可调用对象设置绑定
     * @tparam Functor 可调用对象类型
     * @param f 可调用对象
     * @param location 源代码位置信息
     * @return 之前的绑定对象
     */
    template<typename Functor>
    requires PropertyBindingCallable<Functor, T>
    auto setBinding(Functor &&f, 
                   const PropertyBindingSourceLocation &location = PROPERTY_DEFAULT_BINDING_LOCATION) {
        return setBindingFromFunctor(std::forward<Functor>(f), location);
    }
    
    /**
     * @brief 检查是否有绑定
     * @return 如果有绑定则返回true
     */
    bool hasBinding() const override {
        return bindingData_.hasBinding();
    }
    
    /**
     * @brief 获取当前绑定
     * @return 当前绑定对象
     */
    auto binding() const {
        return getBindingImpl();
    }
    
    /**
     * @brief 移除并返回当前绑定
     * @return 被移除的绑定对象
     */
    auto takeBinding() {
        return takeBindingImpl();
    }

    // ==================================================================================
    // 观察者方法
    // ==================================================================================
    
    /**
     * @brief 添加值变化观察者
     * @tparam Functor 回调函数类型
     * @param f 回调函数
     * @return 观察者对象
     */
    template<typename Functor>
    requires std::invocable<Functor>
    auto onValueChanged(Functor &&f) {
        return createChangeHandler(std::forward<Functor>(f));
    }
    
    /**
     * @brief 订阅属性变化（立即调用一次回调）
     * @tparam Functor 回调函数类型
     * @param f 回调函数
     * @return 观察者对象
     */
    template<typename Functor>
    requires std::invocable<Functor>
    auto subscribe(Functor &&f) {
        f(); // 立即调用一次
        return onValueChanged(std::forward<Functor>(f));
    }
    
    /**
     * @brief 添加通知器
     * @tparam Functor 回调函数类型
     * @param f 回调函数
     * @return 通知器对象
     */
    template<typename Functor>
    requires std::invocable<Functor>
    auto addNotifier(Functor &&f) {
        return createNotifier(std::forward<Functor>(f));
    }

    // ==================================================================================
    // 虚函数重写
    // ==================================================================================
    
    /**
     * @brief 移除绑定（除非在包装器中）
     */
    void removeBindingUnlessInWrapper() override {
        bindingData_.removeBindingUnlessInWrapper();
    }

private:
    // ==================================================================================
    // 私有成员变量
    // ==================================================================================

    PropertyBindingData bindingData_;

    // ==================================================================================
    // 私有辅助方法
    // ==================================================================================
    
    /**
     * @brief 检查值是否相等
     * @param newValue 新值
     * @return 如果相等则返回true
     */
    bool isEqual(const T& newValue) const {
        if constexpr (EqualityComparable<T>) {
            return newValue == this->val;
        }
        return false;
    }
    
    /**
     * @brief 注册到当前正在评估的绑定
     */
    void registerWithCurrentlyEvaluatingBinding() const {
        bindingData_.registerWithCurrentlyEvaluatingBinding();
    }

    /**
     * @brief 通知观察者
     */
    void notifyObservers() {
        bindingData_.notifyObservers(this);
    }

    /**
     * @brief 移除绑定
     */
    void removeBinding() {
        bindingData_.removeBinding();
    }
    
    /**
     * @brief 绑定实现方法的声明
     */
    template<typename BindingType>
    auto setBindingImpl(const BindingType &newBinding) {
        return bindingData_.setBinding(newBinding, this);
    }

    template<typename Functor>
    auto setBindingFromFunctor(Functor &&f, const PropertyBindingSourceLocation &location) {
        using ReturnType = std::invoke_result_t<Functor>;
        static_assert(std::is_same_v<ReturnType, T>, "Binding function return type must match property type");

        // 创建类型化绑定
        PropertyBinding<T> binding(std::forward<Functor>(f), location);
        return bindingData_.setBinding(binding, this);
    }

    auto getBindingImpl() const {
        if (auto* binding = bindingData_.binding()) {
            return PropertyBinding<T>(UntypedPropertyBinding(binding));
        }
        return PropertyBinding<T>();
    }

    auto takeBindingImpl() {
        return PropertyBinding<T>(bindingData_.setBinding(UntypedPropertyBinding(), this));
    }

    template<typename Functor>
    auto createChangeHandler(Functor &&f) {
        // 简化实现：暂时返回空对象
        struct DummyHandler {};
        return DummyHandler{};
    }

    template<typename Functor>
    auto createNotifier(Functor &&f) {
        // 简化实现：暂时返回空对象
        struct DummyNotifier {};
        return DummyNotifier{};
    }
    
    // 禁用拷贝和移动
    Property(const Property&) = delete;
    Property(Property&&) = delete;
    Property& operator=(const Property&) = delete;
    Property& operator=(Property&&) = delete;
};

} // namespace PropertySystem::Core

// ==================================================================================
// 全局命名空间别名
// ==================================================================================

using PropertySystem::Core::Property;

#endif // PROPERTY_SYSTEM_CORE_PROPERTY_H
