/**
 * @file Utility/UpdateGroup.h
 * @brief 属性更新组工具
 * 
 * 这个文件提供了批量属性更新的工具类，
 * 支持事务性的属性修改和回滚功能。
 */

#ifndef PROPERTY_SYSTEM_UTILITY_UPDATE_GROUP_H
#define PROPERTY_SYSTEM_UTILITY_UPDATE_GROUP_H

#include "../Core/PropertyTypes.h"
#include <vector>
#include <functional>
#include <memory>

namespace PropertySystem::Utility {

// ==================================================================================
// 前向声明
// ==================================================================================

class PropertyUpdateGroupPrivate;

// ==================================================================================
// 属性更新组
// ==================================================================================

/**
 * @brief 属性更新组类
 * 
 * 这个类提供了批量更新多个属性的功能，
 * 支持事务性操作和自动回滚。
 */
class PropertyUpdateGroup {
public:
    /**
     * @brief 默认构造函数
     */
    PropertyUpdateGroup();

    /**
     * @brief 析构函数
     */
    ~PropertyUpdateGroup();

    /**
     * @brief 移动构造函数
     * @param other 另一个更新组对象
     */
    PropertyUpdateGroup(PropertyUpdateGroup&& other) noexcept;

    /**
     * @brief 移动赋值运算符
     * @param other 另一个更新组对象
     * @return 当前对象的引用
     */
    PropertyUpdateGroup& operator=(PropertyUpdateGroup&& other) noexcept;

    /**
     * @brief 添加属性到更新组
     * @tparam PropertyType 属性类型
     * @param property 属性指针
     * @param newValue 新值
     */
    template<typename PropertyType>
    void addUpdate(PropertyType* property, const typename PropertyType::value_type& newValue) {
        addUpdateImpl(static_cast<Core::UntypedPropertyData*>(property), 
                     [property, newValue]() {
                         property->setValueBypassingBindings(newValue);
                     });
    }

    /**
     * @brief 添加自定义更新操作
     * @param updateFunction 更新函数
     */
    void addCustomUpdate(std::function<void()> updateFunction);

    /**
     * @brief 执行所有更新操作
     * @return 执行是否成功
     */
    bool execute();

    /**
     * @brief 回滚所有更新操作
     */
    void rollback();

    /**
     * @brief 清除所有更新操作
     */
    void clear();

    /**
     * @brief 检查更新组是否为空
     * @return 如果没有更新操作则返回true
     */
    bool isEmpty() const;

    /**
     * @brief 获取更新操作数量
     * @return 更新操作数量
     */
    size_t count() const;

private:
    void addUpdateImpl(Core::UntypedPropertyData* property, std::function<void()> updateFunction);

    std::unique_ptr<PropertyUpdateGroupPrivate> d;

    // 禁用拷贝构造和拷贝赋值
    PropertyUpdateGroup(const PropertyUpdateGroup&) = delete;
    PropertyUpdateGroup& operator=(const PropertyUpdateGroup&) = delete;
};

// ==================================================================================
// 作用域属性更新组
// ==================================================================================

/**
 * @brief 作用域属性更新组
 * 
 * 这个类提供了RAII风格的属性更新组，
 * 在析构时自动执行或回滚更新。
 */
class ScopedPropertyUpdateGroup {
public:
    /**
     * @brief 构造函数
     * @param autoExecute 是否在析构时自动执行更新
     */
    explicit ScopedPropertyUpdateGroup(bool autoExecute = true);

    /**
     * @brief 析构函数
     */
    ~ScopedPropertyUpdateGroup();

    /**
     * @brief 添加属性更新
     * @tparam PropertyType 属性类型
     * @param property 属性指针
     * @param newValue 新值
     */
    template<typename PropertyType>
    void addUpdate(PropertyType* property, const typename PropertyType::value_type& newValue) {
        updateGroup_.addUpdate(property, newValue);
    }

    /**
     * @brief 添加自定义更新操作
     * @param updateFunction 更新函数
     */
    void addCustomUpdate(std::function<void()> updateFunction) {
        updateGroup_.addCustomUpdate(std::move(updateFunction));
    }

    /**
     * @brief 手动执行更新
     * @return 执行是否成功
     */
    bool execute();

    /**
     * @brief 手动回滚更新
     */
    void rollback();

    /**
     * @brief 设置是否自动执行
     * @param autoExecute 是否自动执行
     */
    void setAutoExecute(bool autoExecute) { autoExecute_ = autoExecute; }

    /**
     * @brief 检查是否设置了自动执行
     * @return 如果设置了自动执行则返回true
     */
    bool isAutoExecute() const { return autoExecute_; }

    /**
     * @brief 获取底层更新组
     * @return 更新组的引用
     */
    PropertyUpdateGroup& updateGroup() { return updateGroup_; }

private:
    PropertyUpdateGroup updateGroup_;
    bool autoExecute_;
    bool executed_ = false;

    // 禁用拷贝构造和拷贝赋值
    ScopedPropertyUpdateGroup(const ScopedPropertyUpdateGroup&) = delete;
    ScopedPropertyUpdateGroup& operator=(const ScopedPropertyUpdateGroup&) = delete;
};

// ==================================================================================
// 便利函数
// ==================================================================================

/**
 * @brief 创建属性更新组的便利函数
 * @return PropertyUpdateGroup对象
 */
inline PropertyUpdateGroup makeUpdateGroup() {
    return PropertyUpdateGroup();
}

/**
 * @brief 创建作用域更新组的便利函数
 * @param autoExecute 是否自动执行
 * @return ScopedPropertyUpdateGroup对象
 */
inline ScopedPropertyUpdateGroup makeScopedUpdateGroup(bool autoExecute = true) {
    return ScopedPropertyUpdateGroup(autoExecute);
}

/**
 * @brief 批量更新多个属性的便利函数
 * @tparam PropertyTypes 属性类型包
 * @param properties 属性指针包
 * @param values 新值包
 * @return 更新是否成功
 */
template<typename... PropertyTypes, typename... ValueTypes>
bool updateProperties(PropertyTypes*... properties, const ValueTypes&... values) {
    static_assert(sizeof...(PropertyTypes) == sizeof...(ValueTypes), 
                 "Number of properties must match number of values");
    
    PropertyUpdateGroup group;
    (group.addUpdate(properties, values), ...);
    return group.execute();
}

/**
 * @brief 在作用域内批量更新属性的便利函数
 * @param updateFunction 包含更新操作的函数
 * @return 更新是否成功
 */
template<typename UpdateFunction>
bool withPropertyUpdates(UpdateFunction&& updateFunction) {
    ScopedPropertyUpdateGroup group(false);
    updateFunction(group);
    return group.execute();
}

// ==================================================================================
// 更新策略
// ==================================================================================

/**
 * @brief 属性更新策略枚举
 */
enum class PropertyUpdateStrategy {
    Immediate,    ///< 立即更新
    Deferred,     ///< 延迟更新
    Batched,      ///< 批量更新
    Conditional   ///< 条件更新
};

/**
 * @brief 属性更新策略管理器
 */
class PropertyUpdateStrategyManager {
public:
    /**
     * @brief 设置全局更新策略
     * @param strategy 更新策略
     */
    static void setGlobalStrategy(PropertyUpdateStrategy strategy);

    /**
     * @brief 获取全局更新策略
     * @return 当前的全局更新策略
     */
    static PropertyUpdateStrategy getGlobalStrategy();

    /**
     * @brief 临时设置更新策略
     * @param strategy 临时策略
     * @param scope 作用域函数
     */
    template<typename ScopeFunction>
    static void withStrategy(PropertyUpdateStrategy strategy, ScopeFunction&& scope) {
        auto oldStrategy = getGlobalStrategy();
        setGlobalStrategy(strategy);
        try {
            scope();
        } catch (...) {
            setGlobalStrategy(oldStrategy);
            throw;
        }
        setGlobalStrategy(oldStrategy);
    }

private:
    static PropertyUpdateStrategy globalStrategy_;
};

} // namespace PropertySystem::Utility

// ==================================================================================
// 全局命名空间别名（向后兼容）
// ==================================================================================

using PropertySystem::Utility::PropertyUpdateGroup;
using PropertySystem::Utility::PropertyUpdateStrategy;
using PropertySystem::Utility::PropertyUpdateStrategyManager;

// 便利函数别名
using PropertySystem::Utility::makeUpdateGroup;
using PropertySystem::Utility::makeScopedUpdateGroup;
using PropertySystem::Utility::updateProperties;
using PropertySystem::Utility::withPropertyUpdates;

#endif // PROPERTY_SYSTEM_UTILITY_UPDATE_GROUP_H
