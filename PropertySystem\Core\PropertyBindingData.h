/**
 * @file Core/PropertyBindingData.h
 * @brief 属性绑定数据管理
 * 
 * 这个文件实现了Property系统的绑定数据管理，
 * 负责绑定的存储、观察者管理和通知机制。
 */

#ifndef PROPERTY_SYSTEM_CORE_PROPERTY_BINDING_DATA_H
#define PROPERTY_SYSTEM_CORE_PROPERTY_BINDING_DATA_H

#include "PropertyTypes.h"
#include <memory>
#include <cstdint>
#include <atomic>

namespace PropertySystem::Core::Internal {

// ==================================================================================
// 前向声明
// ==================================================================================

class PropertyBindingPrivate;
class PropertyObserver;
struct PropertyObserverPointer;
struct BindingEvaluationState;
class UntypedPropertyBinding;

// ==================================================================================
// 属性绑定数据类
// ==================================================================================

/**
 * @brief 属性绑定数据管理类
 * 
 * 这个类负责管理单个属性的绑定数据，包括：
 * - 绑定对象的存储和管理
 * - 观察者链表的维护
 * - 通知机制的实现
 * - 延迟通知的支持
 */
class PropertyBindingData {
public:
    // ==================================================================================
    // 构造和析构
    // ==================================================================================
    
    /**
     * @brief 默认构造函数
     */
    PropertyBindingData();
    
    /**
     * @brief 移动构造函数
     * @param other 其他对象
     */
    PropertyBindingData(PropertyBindingData &&other) noexcept;
    
    /**
     * @brief 析构函数
     */
    ~PropertyBindingData();
    
    // 禁用拷贝
    PropertyBindingData(const PropertyBindingData&) = delete;
    PropertyBindingData& operator=(const PropertyBindingData&) = delete;
    PropertyBindingData& operator=(PropertyBindingData&&) = delete;

    // ==================================================================================
    // 绑定管理
    // ==================================================================================
    
    /**
     * @brief 检查是否有绑定
     * @return 如果有绑定则返回true
     */
    bool hasBinding() const;
    
    /**
     * @brief 设置新的绑定
     * @param newBinding 新的绑定对象
     * @param propertyDataPtr 属性数据指针
     * @return 之前的绑定对象
     */
    UntypedPropertyBinding setBinding(const UntypedPropertyBinding &newBinding,
                                     UntypedPropertyData *propertyDataPtr);
    
    /**
     * @brief 获取当前绑定
     * @return 当前绑定的私有数据指针
     */
    PropertyBindingPrivate* binding() const;
    
    /**
     * @brief 移除绑定
     */
    void removeBinding();
    
    /**
     * @brief 移除绑定（除非在包装器中）
     */
    void removeBindingUnlessInWrapper();

    // ==================================================================================
    // 观察者管理
    // ==================================================================================
    
    /**
     * @brief 注册到当前正在评估的绑定
     */
    void registerWithCurrentlyEvaluatingBinding() const;
    
    /**
     * @brief 注册到指定的绑定评估状态
     * @param currentBinding 当前绑定评估状态
     */
    void registerWithCurrentlyEvaluatingBinding(BindingEvaluationState *currentBinding) const;
    
    /**
     * @brief 通知所有观察者
     * @param propertyDataPtr 属性数据指针
     */
    void notifyObservers(UntypedPropertyData *propertyDataPtr) const;
    
    /**
     * @brief 添加观察者
     * @param observer 观察者指针
     */
    void addObserver(PropertyObserver *observer);
    
    /**
     * @brief 移除观察者
     * @param observer 观察者指针
     */
    void removeObserver(PropertyObserver *observer);

    // ==================================================================================
    // 状态查询
    // ==================================================================================
    
    /**
     * @brief 检查通知是否被延迟
     * @return 如果通知被延迟则返回true
     */
    bool isNotificationDelayed() const;
    
    /**
     * @brief 获取观察者数量
     * @return 观察者数量
     */
    size_t observerCount() const;

    // ==================================================================================
    // 内部接口
    // ==================================================================================
    
    /**
     * @brief 获取内部数据引用（内部使用）
     * @return 内部数据的引用
     */
    std::uintptr_t& d_ref() const;
    
    /**
     * @brief 获取内部数据（内部使用）
     * @return 内部数据
     */
    std::uintptr_t d() const;

private:
    // ==================================================================================
    // 私有成员变量
    // ==================================================================================
    
    // 使用tagged pointer技术来存储绑定和观察者信息
    // 最低位：BindingBit - 指示是否指向绑定对象
    // 次低位：DelayedNotificationBit - 指示是否使用延迟通知
    mutable std::atomic<std::uintptr_t> d_ptr_{0};
    
    // ==================================================================================
    // 常量定义
    // ==================================================================================
    
    static constexpr std::uintptr_t BindingBit = 0x1;              ///< 绑定标志位
    static constexpr std::uintptr_t DelayedNotificationBit = 0x2;  ///< 延迟通知标志位
    static constexpr std::uintptr_t TagMask = 0x3;                 ///< 标志位掩码
    static constexpr std::uintptr_t PointerMask = ~TagMask;        ///< 指针掩码

    // ==================================================================================
    // 私有辅助方法
    // ==================================================================================
    
    /**
     * @brief 设置绑定的辅助方法
     * @param binding 绑定私有数据
     */
    void setBindingHelper(PropertyBindingPrivate *binding);
    
    /**
     * @brief 移除绑定的辅助方法
     */
    void removeBindingHelper();
    
    /**
     * @brief 注册依赖的辅助方法
     * @param currentBinding 当前绑定评估状态
     */
    void registerDependencyHelper(BindingEvaluationState *currentBinding) const;
    
    /**
     * @brief 获取第一个观察者
     * @return 第一个观察者的指针
     */
    PropertyObserver* firstObserver() const;
    
    /**
     * @brief 设置第一个观察者
     * @param observer 观察者指针
     */
    void setFirstObserver(PropertyObserver *observer);
    
    /**
     * @brief 清理观察者链表
     */
    void clearObservers();
    
    /**
     * @brief 修复移动后的指针
     */
    void fixupAfterMove();

    // ==================================================================================
    // 友元声明
    // ==================================================================================
    
    friend class PropertyBindingPrivate;
    friend struct PropertyObserverPointer;
    friend class PropertyBindingDataPointer;
    template<typename T> friend class Property;
};

// ==================================================================================
// 属性绑定数据指针辅助类
// ==================================================================================

/**
 * @brief 属性绑定数据指针辅助类
 * 
 * 这个类提供对PropertyBindingData的便利访问接口，
 * 简化了指针操作和类型转换。
 */
class PropertyBindingDataPointer {
public:
    /**
     * @brief 构造函数
     * @param ptr PropertyBindingData指针
     */
    explicit PropertyBindingDataPointer(const PropertyBindingData *ptr) : ptr_(ptr) {}
    
    /**
     * @brief 获取绑定私有数据
     * @return 绑定私有数据指针
     */
    PropertyBindingPrivate* binding() const;
    
    /**
     * @brief 获取第一个观察者
     * @return 第一个观察者指针
     */
    PropertyObserver* firstObserver() const;
    
    /**
     * @brief 设置第一个观察者
     * @param observer 观察者指针
     */
    void setFirstObserver(PropertyObserver *observer);
    
    /**
     * @brief 添加观察者
     * @param observer 观察者指针
     */
    void addObserver(PropertyObserver *observer);
    
    /**
     * @brief 获取观察者数量
     * @return 观察者数量
     */
    size_t observerCount() const;
    
    /**
     * @brief 修复移动后的指针
     * @param ptr PropertyBindingData指针
     */
    static void fixupAfterMove(PropertyBindingData *ptr);

private:
    const PropertyBindingData *ptr_;
};

} // namespace PropertySystem::Core::Internal

// ==================================================================================
// 全局命名空间别名
// ==================================================================================

using PropertySystem::Core::Internal::PropertyBindingData;
using PropertySystem::Core::Internal::PropertyBindingDataPointer;

#endif // PROPERTY_SYSTEM_CORE_PROPERTY_BINDING_DATA_H
