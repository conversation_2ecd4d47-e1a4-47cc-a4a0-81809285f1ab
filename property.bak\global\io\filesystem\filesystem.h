﻿#ifndef FILESYSTEM_H
#define FILESYSTEM_H

#include <string>
#include <memory>
#include <vector>

#include "file.h"
#include "folder.h"
#include "fileinfo.h"
#include "fileinfogatherer.h"

namespace filesystem {

/**
 * @brief The FileSystem class provides a high-level interface for file system operations.
 * 
 * FileSystem provides methods for creating, opening, and manipulating files and directories,
 * as well as gathering file information.
 */
class FileSystem {
public:
    /**
     * @brief Constructs a FileSystem.
     */
    FileSystem();

    /**
     * @brief Destructor.
     */
    ~FileSystem();

    /**
     * @brief Creates a file.
     * @param path The path to the file.
     * @param overwrite Whether to overwrite an existing file.
     * @return True if the file was created successfully, false otherwise.
     */
    bool createFile(const std::string& path, bool overwrite = false);

    /**
     * @brief Creates a directory.
     * @param path The path to the directory.
     * @param createParents Whether to create parent directories as needed.
     * @return True if the directory was created successfully, false otherwise.
     */
    bool createDirectory(const std::string& path, bool createParents = false);

    /**
     * @brief Removes a file.
     * @param path The path to the file.
     * @return True if the file was removed successfully, false otherwise.
     */
    bool removeFile(const std::string& path);

    /**
     * @brief Removes a directory.
     * @param path The path to the directory.
     * @param recursive Whether to remove the directory recursively.
     * @return True if the directory was removed successfully, false otherwise.
     */
    bool removeDirectory(const std::string& path, bool recursive = false);

    /**
     * @brief Copies a file.
     * @param source The source file.
     * @param destination The destination file.
     * @param overwrite Whether to overwrite an existing file.
     * @return True if the file was copied successfully, false otherwise.
     */
    bool copyFile(const std::string& source, const std::string& destination, bool overwrite = false);

    /**
     * @brief Moves a file.
     * @param source The source file.
     * @param destination The destination file.
     * @param overwrite Whether to overwrite an existing file.
     * @return True if the file was moved successfully, false otherwise.
     */
    bool moveFile(const std::string& source, const std::string& destination, bool overwrite = false);

    /**
     * @brief Copies a directory.
     * @param source The source directory.
     * @param destination The destination directory.
     * @param recursive Whether to copy the directory recursively.
     * @return True if the directory was copied successfully, false otherwise.
     */
    bool copyDirectory(const std::string& source, const std::string& destination, bool recursive = true);

    /**
     * @brief Moves a directory.
     * @param source The source directory.
     * @param destination The destination directory.
     * @return True if the directory was moved successfully, false otherwise.
     */
    bool moveDirectory(const std::string& source, const std::string& destination);

    /**
     * @brief Returns whether a file exists.
     * @param path The path to the file.
     * @return True if the file exists, false otherwise.
     */
    bool fileExists(const std::string& path);

    /**
     * @brief Returns whether a directory exists.
     * @param path The path to the directory.
     * @return True if the directory exists, false otherwise.
     */
    bool directoryExists(const std::string& path);

    /**
     * @brief Returns information about a file.
     * @param path The path to the file.
     * @return The file information.
     */
    FileInfo getFileInfo(const std::string& path);

    /**
     * @brief Returns the list of entries in a directory.
     * @param path The path to the directory.
     * @param nameFilters List of name filters (e.g., "*.txt").
     * @param includeFiles Whether to include files in the result.
     * @param includeDirs Whether to include directories in the result.
     * @param includeHidden Whether to include hidden entries in the result.
     * @return The list of entries.
     */
    std::vector<FileInfo> getDirectoryEntries(
        const std::string& path,
        const std::vector<std::string>& nameFilters = {},
        bool includeFiles = true,
        bool includeDirs = true,
        bool includeHidden = false);

    /**
     * @brief Opens a file.
     * @param path The path to the file.
     * @param mode The open mode.
     * @return A shared pointer to the file, or nullptr if the file could not be opened.
     */
    std::shared_ptr<File> openFile(const std::string& path, File::OpenMode mode);

    /**
     * @brief Opens a directory.
     * @param path The path to the directory.
     * @return A shared pointer to the directory, or nullptr if the directory could not be opened.
     */
    std::shared_ptr<Folder> openDirectory(const std::string& path);

    /**
     * @brief Returns the current working directory.
     * @return The current working directory.
     */
    std::string currentDirectory();

    /**
     * @brief Sets the current working directory.
     * @param path The new current working directory.
     * @return True if the current working directory was set successfully, false otherwise.
     */
    bool setCurrentDirectory(const std::string& path);

    /**
     * @brief Returns the error string for the last error.
     * @return The error string, or an empty string if no error occurred.
     */
    std::string errorString() const;

private:
    std::string m_errorString;
    std::unique_ptr<FileInfoGatherer> m_fileInfoGatherer;
};

} // namespace filesystem

#endif // FILESYSTEM_H
