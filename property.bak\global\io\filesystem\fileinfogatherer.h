﻿#ifndef FILEINFOGATHERER_H
#define FILEINFOGATHERER_H

#include <string>
#include <vector>
#include <functional>
#include <atomic>
#include <thread>
#include <mutex>
#include <condition_variable>
#include <future>

#include "fileinfo.h"

namespace filesystem {

/**
 * @brief The FileInfoGatherer class gathers information about files in a directory asynchronously.
 *
 * FileInfoGatherer provides asynchronous gathering of file information,
 * with support for cancellation and progress reporting.
 */
class FileInfoGatherer
{
public:
    /**
     * @brief Defines the type of file gathering operation.
     */
    enum class GatherType {
        SingleDirectory,    ///< Gather files in a single directory
        Recursive           ///< Gather files recursively
    };

    /**
     * @brief Callback types for file gathering events.
     */
    using UpdatesCallback = std::function<void(const std::string& path, const std::vector<FileInfo>& updates)>;
    using DirectoryLoadedCallback = std::function<void(const std::string& path)>;
    using ErrorCallback = std::function<void(const std::string& path, const std::string& error)>;
    using ProgressCallback = std::function<void(int filesProcessed, int totalFiles)>;

    /**
     * @brief Constructs a FileInfoGatherer.
     */
    FileInfoGatherer() = default;

    /**
     * @brief Destructor.
     */
    ~FileInfoGatherer();

    /**
     * @brief Disable copy and move operations.
     */
    FileInfoGatherer(const FileInfoGatherer&) = delete;
    FileInfoGatherer& operator=(const FileInfoGatherer&) = delete;
    FileInfoGatherer(FileInfoGatherer&&) = delete;
    FileInfoGatherer& operator=(FileInfoGatherer&&) = delete;

    // Callback types
    using UpdatesCallback = std::function<void(const std::string& path, const std::vector<FileInfo>& updates)>;
    using DirectoryLoadedCallback = std::function<void(const std::string& path)>;

    /**
     * @brief Sets the callback for file updates.
     * @param callback The callback function.
     */
    void setUpdatesCallback(UpdatesCallback callback);

    /**
     * @brief Sets the callback for directory loaded events.
     * @param callback The callback function.
     */
    void setDirectoryLoadedCallback(DirectoryLoadedCallback callback);

    /**
     * @brief Sets the callback for error events.
     * @param callback The callback function.
     */
    void setErrorCallback(ErrorCallback callback);

    /**
     * @brief Sets the callback for progress events.
     * @param callback The callback function.
     */
    void setProgressCallback(ProgressCallback callback);

    /**
     * @brief Gathers file information synchronously.
     * @param path The path to gather information from.
     * @param type The type of gathering operation.
     * @param nameFilters List of name filters (e.g., "*.txt").
     * @param includeFiles Whether to include files in the result.
     * @param includeDirs Whether to include directories in the result.
     * @return The gathered file information.
     */
    std::vector<FileInfo> getFileInfos(
        const std::string& path,
        GatherType type = GatherType::SingleDirectory,
        const std::vector<std::string>& nameFilters = {},
        bool includeFiles = true,
        bool includeDirs = true);

    /**
     * @brief Starts gathering file information asynchronously.
     * @param path The path to gather information from.
     * @param type The type of gathering operation.
     * @param nameFilters List of name filters (e.g., "*.txt").
     * @param includeFiles Whether to include files in the result.
     * @param includeDirs Whether to include directories in the result.
     * @return A future that will be set when the operation is complete.
     */
    std::future<std::vector<FileInfo>> getFileInfosAsync(
        const Path& path,
        GatherType type = GatherType::SingleDirectory,
        const std::vector<std::string>& nameFilters = {},
        bool includeFiles = true,
        bool includeDirs = true);

    /**
     * @brief Aborts the current gathering operation.
     */
    void abort();

    /**
     * @brief Returns whether a gathering operation is in progress.
     * @return True if a gathering operation is in progress, false otherwise.
     */
    bool isRunning() const;

    /**
     * @brief Waits for the current gathering operation to complete.
     * @param timeout The maximum time to wait in milliseconds. 0 means wait indefinitely.
     * @return True if the operation completed, false if the timeout expired.
     */
    bool wait(int timeout = 0);

private:
    /**
     * @brief Worker function for gathering file information.
     * @param path The path to gather information from.
     * @param type The type of gathering operation.
     * @param nameFilters List of name filters.
     * @param includeFiles Whether to include files.
     * @param includeDirs Whether to include directories.
     * @param promise The promise to set with the result.
     */
    void gatherWorker(
        const Path& path,
        GatherType type,
        const std::vector<std::string>& nameFilters,
        bool includeFiles,
        bool includeDirs,
        std::promise<std::vector<FileInfo>> promise);

    /**
     * @brief Processes a directory and gathers file information.
     * @param path The path to process.
     * @param type The type of gathering operation.
     * @param nameFilters List of name filters.
     * @param includeFiles Whether to include files.
     * @param includeDirs Whether to include directories.
     * @param result The vector to store the results in.
     */
    void processDirectory(
        const Path& path,
        GatherType type,
        const std::vector<std::string>& nameFilters,
        bool includeFiles,
        bool includeDirs,
        std::vector<FileInfo>& result);

    /**
     * @brief Checks if a file name matches any of the filters.
     * @param name The file name to check.
     * @param filters The filters to check against.
     * @return True if the file name matches any filter, false otherwise.
     */
    bool matchesFilter(const std::string& name, const std::vector<std::string>& filters);

    std::atomic<bool> m_abort{false};
    std::atomic<bool> m_running{false};
    std::thread m_workerThread;
    std::mutex m_mutex;
    std::condition_variable m_condition;

    UpdatesCallback m_updatesCallback;
    DirectoryLoadedCallback m_directoryLoadedCallback;
    ErrorCallback m_errorCallback;
    ProgressCallback m_progressCallback;

    int m_filesProcessed{0};
    int m_totalFiles{0};
    bool m_hasCalledUpdate{false};
    std::chrono::steady_clock::time_point m_lastUpdateTime;

}; // class FileInfoGatherer

} // namespace filesystem

#endif // FILEINFOGATHERER_H
