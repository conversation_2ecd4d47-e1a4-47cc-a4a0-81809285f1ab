/*
igor<PERSON><PERSON><PERSON>/kors_modularity
https://github.com/igor<PERSON><PERSON>kov/kors_modularity/
*/

#ifndef MODULARITY_IMODULEINTERFACE_H
#define MODULARITY_IMODULEINTERFACE_H

#include <memory>

#include "moduleinfo.h"

namespace modularity {
class IModuleInterface
{
public:
    virtual ~IModuleInterface() = default;
};

class IModuleExportInterface : public IModuleInterface
{
public:
    virtual ~IModuleExportInterface() = default;

    static constexpr bool isInternalInterface() { return false; }
};

class IModuleInternalInterface : public IModuleInterface
{
public:
    virtual ~IModuleInternalInterface() = default;

    static constexpr bool isInternalInterface() { return true; }
};

class IModuleCreator
{
public:
    virtual ~IModuleCreator() = default;
    virtual std::shared_ptr<IModuleInterface> create() = 0;
};

struct IModuleExportCreator : public IModuleCreator {
    virtual ~IModuleExportCreator() = default;
    static constexpr bool isInternalInterface() { return false; }
};

struct IModuleInternalCreator : public IModuleCreator {
    virtual ~IModuleInternalCreator() = default;
    static constexpr bool isInternalInterface() { return true; }
};

#define INTERFACE_ID(id)                                                \
public:                                                                 \
    static const modularity::InterfaceInfo& interfaceInfo() {       \
        static const modularity::InterfaceInfo info(#id, MODULENAME, isInternalInterface());    \
        return info;                                                    \
    }                                                                   \
private:
}

#define MODULE_EXPORT_INTERFACE public modularity::IModuleExportInterface
//#define MODULE_EXPORT_CREATOR public modularity::IModuleExportCreator

#define MODULE_INTERNAL_INTERFACE public modularity::IModuleInternalInterface
//#define MODULE_INTERNAL_CREATOR public modularity::IModuleInternalCreator

#endif // MODULARITY_IMODULEINTERFACE_H
