﻿#include "fileinfogatherer.h"

namespace fs = std::filesystem;

FileInfoGatherer::FileInfoGatherer(QObject* parent)
    : QObject{parent}
{
}

FileInfoGatherer::~FileInfoGatherer()
{
    qDebug() << "FileInfoGatherer: Destructor.";
}

void FileInfoGatherer::getFileInfos(const QString& path)
{
    QElapsedTimer startTime;
    startTime.start();
    QElapsedTimer base;
    base.start();
    bool firstTime = true;
    int level = 0;
    m_updatedFiles.clear();

    getFiles(path, level, base, firstTime);

    if (!m_updatedFiles.empty()) {
        emit updates(path, m_updatedFiles);
    }

    qDebug().noquote() << QDateTime::currentDateTime().toString("[hh:mm:ss]")
                       << "File info gathered. "
                       << path << startTime.elapsed() << "ms";
    emit directoryLoaded(path);
}

/*void FileInfoGatherer::getFiles(const QString& path, int& level, QElapsedTimer& base, bool& firstTime)
{
    fs::path dirPath(path.toStdU16String()); // 处理 Unicode 路径

    try {
        auto dirIt = fs::directory_iterator(dirPath, fs::directory_options::skip_permission_denied);
        for (const auto& entry : dirIt) {
            if (m_abort.loadRelaxed()) break;

            // 跳过 "." 和 ".."
            const auto& filename = entry.path().filename();
            if (filename == "." || filename == "..") continue;

            // 转换为 QFileInfo 并刷新属性
            QFileInfo fileInfo(QString::fromStdU16String(entry.path().u16string()));
            fileInfo.refresh(); // 强制刷新文件属性

            fetch(path, fileInfo, base, firstTime);
        }
    } catch (const fs::filesystem_error& e) {
        qWarning() << "Filesystem error:" << e.what();
    }
}*/

void FileInfoGatherer::getFiles(const QString& path, int& level, QElapsedTimer& base, bool& firstTime)
{
    QFileInfo fileInfo;
    QDirIterator dirIt(path, QDir::AllEntries | QDir::System | QDir::Hidden | QDir::NoDotAndDotDot);
    while (!m_abort.loadRelaxed() && dirIt.hasNext()) {
        fileInfo = dirIt.nextFileInfo();
        //fileInfo.stat();  // 这个操作消耗大量时间
        fetch(path, fileInfo, base, firstTime);

        // 递归子文件夹
        // if (level > 0 and fileInfo.isDir()) {
        //     level -= 1;
        //     getFiles(fileInfo.filePath(), level, base, firstTime);
        // }
    }
}

void FileInfoGatherer::fetch(const QString& path, const QFileInfo& fileInfo, QElapsedTimer& base, bool& firstTime)
{
    //m_allFiles.append(fileInfo.fileName());
    m_updatedFiles.push_back(fileInfo);
    QElapsedTimer current;
    current.start();
    if ((firstTime && m_updatedFiles.size() > 100) || base.msecsTo(current) > 1000) {
        emit updates(path, m_updatedFiles);
        m_updatedFiles.clear();
        base = current;
        firstTime = false;
    }
}
