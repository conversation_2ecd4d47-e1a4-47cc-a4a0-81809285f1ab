#include "PropertySystem/PropertySystem.h"
#include <iostream>

int main() {
    std::cout << "PropertySystem version: " << PropertySystem::getPropertySystemVersion() << std::endl;
    std::cout << "Build info: " << PropertySystem::getPropertySystemBuildInfo() << std::endl;
    
    // 测试基本的属性创建
    PropertySystem::PropertyData<int> intProperty(42);
    std::cout << "Integer property value: " << intProperty.value() << std::endl;
    
    // 测试概念
    static_assert(PropertySystem::Core::PropertyType<int>);
    static_assert(PropertySystem::Core::EqualityComparable<int>);
    
    std::cout << "PropertySystem compilation test passed!" << std::endl;
    return 0;
}
