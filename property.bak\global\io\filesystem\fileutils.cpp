﻿#include "fileutils.h"

#include <iostream>
#include <iomanip>
#include <sstream>

#ifdef _WIN32
#include <windows.h>
#include <shellapi.h>
#elif defined(__APPLE__)
#include <cstdlib>
#elif defined(__linux__)
#include <cstdlib>
#endif

#ifdef QT_CORE_LIB
#include <qDebug>
#include <QUrl>
#include <QProcess>
#include <QDir>
#include <QFileInfo>
#include <QDesktopServices>
#endif

namespace fileutils {
std::string readableFileSize(const int bytes, const int precision)
{
    static const std::vector<std::string> units = { "B", "KiB", "MiB", "GiB", "TiB", "PiB", "EiB", "ZiB" };

    double size = bytes;
    std::string unit = "B";
    for (const auto& u : units) {
        if (size < 1024.0) {
            unit = u;
            break;
        }
        size /= 1024.0;
    }
    std::ostringstream oss;
    oss << std::fixed << std::setprecision(precision) << size << " " << unit;
    return oss.str();
}

std::vector<std::string> pathSplit(const std::string& input)
{
    std::string path = input;
    // 替换"\\"为"/"
    size_t pos = 0;
    while ((pos = path.find("\\", pos)) != std::string::npos) {
        path.replace(pos, 1, "/");
        pos++;
    }
    // 按"/"分割
    std::vector<std::string> parts;
    std::istringstream iss(path);
    std::string token;
    while (std::getline(iss, token, '/')) {
        if (!token.empty()) {
            parts.push_back(token);
        }
    }
    return parts;
}
}

bool FileUtils::showInGraphicalShell(const std::string& path)
{
#ifdef QT_CORE_LIB
    // https://github.com/qt-creator/qt-creator/blob/master/src/plugins/coreplugin/fileutils.cpp#L54
    // https://stackoverflow.com/a/46019091/22058285
    QFileInfo fileInfo(path);
#if defined(Q_OS_WIN)
    QStringList args;
    if (!fileInfo.isDir()) {
        //showInExplorer(path); return;
        args << QLatin1String("/select,");
    }
    args << QDir::toNativeSeparators(fileInfo.canonicalFilePath());
    if (QProcess::startDetached("explorer.exe", args)) {
        return false;
    }
#elif defined(Q_OS_MAC)
    // from QtCreator source code
    // Process::startDetached({ "/usr/bin/open", { "-R", fileInfo.canonicalFilePath() } });
    /* from https://stackoverflow.com/a/46019091/22058285*/
    QStringList args;
    args << "-e";
    args << "tell application \"Finder\"";
    args << "-e";
    args << "activate";
    args << "-e";
    args << "select POSIX file \"" + path + "\"";
    args << "-e";
    args << "end tell";
    args << "-e";
    args << "return";
    if (!QProcess::execute("/usr/bin/osascript", args))
        return false;

#endif
    QDesktopServices::openUrl(QUrl::fromLocalFile(fileInfo.isDir() ? path : fileInfo.path()));
    return true;
}
#else
#ifdef _WIN32
    // Windows implementation
    std::string command = "explorer.exe /select,\"" + path + "\"";
    return (system(command.c_str()) == 0);
#elif defined(__APPLE__)
    // macOS implementation
    std::string command = "open -R \"" + path + "\"";
    return (system(command.c_str()) == 0);
#elif defined(__linux__)
    // Linux implementation - try with common file managers
    std::string dirPath = path;
    // Get directory path if it's a file
    std::ifstream file(path);
    if (file.good()) {
        size_t lastSlash = path.find_last_of("/\\");
        if (lastSlash != std::string::npos) {
            dirPath = path.substr(0, lastSlash);
        }
    }

    // Try with common file managers
    std::string command = "xdg-open \"" + dirPath + "\"";
    return (system(command.c_str()) == 0);
#else
    // Unsupported platform
    return false;
#endif
#endif
}

bool FileUtils::open(const std::string& str)
{
#ifdef QT_CORE_LIB
    QString path = QString::fromStdString(str);
    if (QFileInfo::exists(path)) {
        QUrl url = QUrl::fromLocalFile(path);
        if (url.isLocalFile()) {
            QDesktopServices::openUrl(url);
        }
    }
    return false;
#else
#ifdef _WIN32
    // Windows implementation
    ShellExecuteA(NULL, "open", str.c_str(), NULL, NULL, SW_SHOWNORMAL);
    return true;
#elif defined(__APPLE__) || defined(__linux__)
    // macOS and Linux implementation
    std::string command;
#if defined(__APPLE__)
    command = "open \"" + str + "\"";
#else
    command = "xdg-open \"" + str + "\"";
#endif
    return (system(command.c_str()) == 0);
#else
    // Unsupported platform
    return false;
#endif
#endif
}

void FileUtils::showInExplorer(const std::string& pathIn) const
{
    (void)pathIn;
    // https://stackoverflow.com/a/3010871/22058285
    // https://blog.csdn.net/gongjianbo1992/article/details/129233861
    // 此函数支持非法创建的超长文件名, 但如果路径是文件夹，则会打开两个explorer(其中一个在父文件夹中选中此文件夹)
    /*
    QString path = QStirng::fromStdString(pathIn)
    ::CoInitialize(nullptr);
    QString path = QDir::toNativeSeparators(path);
    std::wstring str = path.toStdWString();
    PIDLIST_ABSOLUTE pidl = ::ILCreateFromPathW((PCWSTR)str.c_str());
    if(pidl){
        ::SHOpenFolderAndSelectItems(pidl,0,0,0);
        ::ILFree(pidl);
    }
    ::CoUninitialize();
    */
}
