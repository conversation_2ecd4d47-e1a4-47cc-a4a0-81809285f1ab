/**
 * @file Core/PropertyBindingPrivate.cpp
 * @brief PropertyBindingPrivate类的实现
 * 
 * 这个文件实现了PropertyBindingPrivate类的核心功能，
 * 包括绑定的执行、依赖管理和错误处理。
 */

#include "PropertyBindingPrivate.h"
#include "PropertyBindingError.h"
#include "BindingEvaluationState.h"
#include "../Binding/BindingVTable.h"
#include "../Observer/PropertyObserver.h"
#include <algorithm>

namespace PropertySystem::Core::Internal {

// ==================================================================================
// PropertyBindingPrivate实现
// ==================================================================================

PropertyBindingPrivate::PropertyBindingPrivate(const Binding::ModernBindingFunctionVTable *vtable,
                                              const PropertyBindingSourceLocation &location)
    : vtable_(vtable), location_(location) {
}

PropertyBindingPrivate::~PropertyBindingPrivate() {
    clearDependencyObservers();
}

bool PropertyBindingPrivate::isValid() const {
    return vtable_ != nullptr;
}

PropertyBindingError PropertyBindingPrivate::error() const {
    return error_;
}

PropertyBindingSourceLocation PropertyBindingPrivate::location() const {
    return location_;
}

void PropertyBindingPrivate::setError(PropertyBindingError &&error) {
    error_ = std::move(error);
}

void PropertyBindingPrivate::clearError() {
    error_ = PropertyBindingError();
}

bool PropertyBindingPrivate::isUpdating() const {
    return updating_;
}

void PropertyBindingPrivate::setUpdating(bool updating) {
    updating_ = updating;
}

bool PropertyBindingPrivate::isSticky() const {
    return sticky_;
}

void PropertyBindingPrivate::setSticky(bool sticky) {
    sticky_ = sticky;
}

void PropertyBindingPrivate::scheduleNotify() {
    pending_notify_ = true;
}

bool PropertyBindingPrivate::hasPendingNotify() const {
    return pending_notify_;
}

void PropertyBindingPrivate::prependObserver(PropertyObserver *observer) {
    if (!observer) return;
    
    observer->next = first_observer_;
    if (first_observer_) {
        first_observer_->prev = &observer->next;
    }
    first_observer_ = observer;
    observer->prev = &first_observer_;
}

PropertyObserver* PropertyBindingPrivate::takeObservers() {
    PropertyObserver* observers = first_observer_;
    first_observer_ = nullptr;
    return observers;
}

PropertyObserver* PropertyBindingPrivate::firstObserver() const {
    return first_observer_;
}

void PropertyBindingPrivate::setFirstObserver(PropertyObserver *observer) {
    first_observer_ = observer;
}

void PropertyBindingPrivate::clearDependencyObservers() {
    // 清理内联观察者
    for (size_t i = 0; i < dependency_observer_count_ && i < inline_dependency_observers_.size(); ++i) {
        // 清理观察者
    }
    dependency_observer_count_ = 0;
    
    // 清理堆观察者
    if (heap_observers_) {
        heap_observers_->clear();
    }
}

PropertyObserver* PropertyBindingPrivate::allocateDependencyObserver() {
    if (dependency_observer_count_ < inline_dependency_observers_.size()) {
        ++dependency_observer_count_;
        return &inline_dependency_observers_[dependency_observer_count_ - 1];
    }
    return allocateDependencyObserver_slow();
}

PropertyObserver* PropertyBindingPrivate::allocateDependencyObserver_slow() {
    if (!heap_observers_) {
        heap_observers_ = std::make_unique<HeapObservers>();
    }
    heap_observers_->emplace_back();
    ++dependency_observer_count_;
    return &heap_observers_->back();
}

bool PropertyBindingPrivate::evaluateRecursive(std::vector<PropertyObserver*> &bindingObservers, 
                                              BindingEvaluationState *status) {
    return evaluateRecursive_inline(bindingObservers, status);
}

bool PropertyBindingPrivate::evaluateRecursive_inline(std::vector<PropertyObserver*> &bindingObservers, 
                                                     BindingEvaluationState *status) {
    if (updating_) {
        // 检测到绑定循环
        setError(PropertyBindingError(PropertyBindingError::BindingLoop, "Binding loop detected"));
        return false;
    }
    
    // 设置更新标志
    updating_ = true;
    
    // 创建绑定评估状态
    BindingEvaluationState evaluationFrame(this, status);
    
    bool changed = false;
    
    try {
        // 执行绑定函数
        if (vtable_ && vtable_->call && property_data_ptr_) {
            // 这里需要调用绑定函数
            // 具体实现取决于绑定函数的存储方式
            // 暂时简化处理
            changed = true; // 假设值发生了变化
        }
    } catch (...) {
        setError(PropertyBindingError(PropertyBindingError::EvaluationError, "Binding evaluation failed"));
        updating_ = false;
        return false;
    }
    
    updating_ = false;
    
    // 如果值发生变化，设置待通知标志
    pending_notify_ = pending_notify_ || changed;
    
    if (!changed || !first_observer_) {
        return changed;
    }
    
    // 评估依赖的绑定
    PropertyObserver* observer = first_observer_;
    while (observer) {
        if (observer->next.tag() == PropertyObserver::ObserverNotifiesBinding) {
            bindingObservers.push_back(observer);
        }
        observer = observer->next.data();
    }
    
    return true;
}

void PropertyBindingPrivate::notifyNonRecursive(const std::vector<PropertyObserver*> &bindingObservers) {
    for (PropertyObserver* observer : bindingObservers) {
        if (observer && observer->binding) {
            observer->binding->notifyNonRecursive();
        }
    }
}

PropertyBindingPrivate::NotificationState PropertyBindingPrivate::notifyNonRecursive() {
    if (!pending_notify_) {
        return Sent;
    }
    
    pending_notify_ = false;
    
    // 通知所有观察者
    PropertyObserver* observer = first_observer_;
    while (observer) {
        if (observer->changeHandler) {
            observer->changeHandler(observer, property_data_ptr_);
        }
        observer = observer->next.data();
    }
    
    return Sent;
}

void PropertyBindingPrivate::unlinkAndDeref() {
    // 断开与属性的连接
    property_data_ptr_ = nullptr;
    clearDependencyObservers();
}

PropertyBindingPrivate* PropertyBindingPrivate::currentlyEvaluatingBinding() {
    // 获取当前正在评估的绑定
    // 这需要访问全局状态
    return nullptr; // 简化实现
}

} // namespace PropertySystem::Core::Internal
