#ifndef TYPECONVERTER_H
#define TYPECONVERTER_H

#define IGNORE_MISMATCH_WARNING

#define CONVERTER_FUNC(TYPE, FUNC) \
if (any.type() == typeid(TYPE)) { \
        return FUNC<T>(std::any_cast<TYPE>(any)); \
}

#define TYPE_CONVERTER(PREFIX, INTERVAL, SUFFIX, FUNC) \
    CONVERTER_FUNC(PREFIX bool INTERVAL SUFFIX, FUNC) \
    CONVERTER_FUNC(PREFIX char INTERVAL SUFFIX, FUNC) \
    CONVERTER_FUNC(PREFIX signed char INTERVAL SUFFIX, FUNC) \
    CONVERTER_FUNC(PREFIX unsigned char INTERVAL SUFFIX, FUNC) \
    CONVERTER_FUNC(PREFIX wchar_t INTERVAL SUFFIX, FUNC) \
    /*    CONVERTER_FUNC(PREFIX char8_t INTERVAL SUFFIX, FUNC) \ */ \
    CONVERTER_FUNC(PREFIX char16_t INTERVAL SUFFIX, FUNC) \
    CONVERTER_FUNC(PREFIX char32_t INTERVAL SUFFIX, FUNC) \
    CONVERTER_FUNC(PREFIX short INTERVAL SUFFIX, FUNC) \
    CONVERTER_FUNC(PREFIX unsigned short INTERVAL SUFFIX, FUNC) \
    CONVERTER_FUNC(PREFIX int INTERVAL SUFFIX, FUNC) \
    CONVERTER_FUNC(PREFIX unsigned int INTERVAL SUFFIX, FUNC) \
    CONVERTER_FUNC(PREFIX long INTERVAL SUFFIX, FUNC) \
    CONVERTER_FUNC(PREFIX unsigned long INTERVAL SUFFIX, FUNC) \
    CONVERTER_FUNC(PREFIX long long INTERVAL SUFFIX, FUNC) \
    CONVERTER_FUNC(PREFIX unsigned long long INTERVAL SUFFIX, FUNC) \
    CONVERTER_FUNC(PREFIX float INTERVAL SUFFIX, FUNC) \
    CONVERTER_FUNC(PREFIX double INTERVAL SUFFIX, FUNC) \
    CONVERTER_FUNC(PREFIX long double INTERVAL SUFFIX, FUNC) \
    CONVERTER_FUNC(PREFIX const char* INTERVAL SUFFIX, FUNC) \
    CONVERTER_FUNC(PREFIX char* INTERVAL SUFFIX, FUNC) \
    CONVERTER_FUNC(PREFIX const signed char* INTERVAL SUFFIX, FUNC) \
    CONVERTER_FUNC(PREFIX signed char* INTERVAL SUFFIX, FUNC) \
    CONVERTER_FUNC(PREFIX const unsigned char* INTERVAL SUFFIX, FUNC) \
    CONVERTER_FUNC(PREFIX unsigned char* INTERVAL SUFFIX, FUNC) \
    CONVERTER_FUNC(PREFIX const wchar_t* INTERVAL SUFFIX, FUNC) \
    CONVERTER_FUNC(PREFIX wchar_t* INTERVAL SUFFIX, FUNC) \
    /*    CONVERTER_FUNC(PREFIX const char8_t* INTERVAL SUFFIX, FUNC) \ */ \
    /*    CONVERTER_FUNC(PREFIX char8_t* INTERVAL SUFFIX, FUNC) \ */ \
    CONVERTER_FUNC(PREFIX const char16_t* INTERVAL SUFFIX, FUNC) \
    CONVERTER_FUNC(PREFIX char16_t* INTERVAL SUFFIX, FUNC) \
    CONVERTER_FUNC(PREFIX const char32_t* INTERVAL SUFFIX, FUNC) \
    CONVERTER_FUNC(PREFIX char32_t* INTERVAL SUFFIX, FUNC) \
    CONVERTER_FUNC(PREFIX std::string INTERVAL SUFFIX, FUNC) \
    CONVERTER_FUNC(PREFIX std::wstring INTERVAL SUFFIX, FUNC) \
    CONVERTER_FUNC(PREFIX std::u8string INTERVAL SUFFIX, FUNC) \
    CONVERTER_FUNC(PREFIX std::u16string INTERVAL SUFFIX, FUNC) \
    CONVERTER_FUNC(PREFIX std::u32string INTERVAL SUFFIX, FUNC) \
    CONVERTER_FUNC(PREFIX Variant INTERVAL SUFFIX, FUNC)

#define BASIC_TYPE_CONVERTER(FUNC) \
    TYPE_CONVERTER(, , , FUNC)

#define POINTER_TYPE_CONVERTER(FUNC) \
    TYPE_CONVERTER(, *, , FUNC) \
    TYPE_CONVERTER(, const *, , FUNC)

#define CONTAINER_TYPE_CONVERTER(TYPES, FUNC) \
    TYPE_CONVERTER(TYPES<, , >, FUNC)

#define CONTAINER_CONVERTER_FUNC(CONTAINER, TYPE1, TYPE2, FUNC) \
if (any.type() == typeid(CONTAINER<TYPE1, TYPE2>)) { \
        /*std::cout << "GET(C) " << #CONTAINER << "<" << #TYPE1 << ", " << #TYPE2 << ">" << std::endl;*/ \
        return FUNC<T>(std::any_cast<CONTAINER<TYPE1, TYPE2>>(any)); \
}

// Sort by usage
#define APPLY_CONVERT(FUNC) \
    BASIC_TYPE_CONVERTER(FUNC) \
    POINTER_TYPE_CONVERTER(FUNC) \
    CONTAINER_TYPE_CONVERTER(std::shared_ptr, FUNC) \
    CONTAINER_TYPE_CONVERTER(std::vector, FUNC) \
    CONTAINER_CONVERTER_FUNC(std::map, std::string, Variant, FUNC) \
    CONTAINER_CONVERTER_FUNC(std::unordered_map, std::string, Variant, FUNC) \
// CONTAINER_TYPE_CONVERTER(std::list, FUNC) \
// CONTAINER_CONVERTER_FUNC(std::pair, Variant, Variant, FUNC)\

#endif // TYPECONVERTER_H
