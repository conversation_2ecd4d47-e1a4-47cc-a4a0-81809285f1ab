/**
 * @file test_minimal.cpp
 * @brief 最小化Property系统测试
 */

#include "PropertySystem/Core/PropertyData.h"
#include <iostream>

int main() {
    std::cout << "=== Minimal Property Test ===" << std::endl;
    
    try {
        // 测试基础PropertyData
        PropertySystem::Core::PropertyData<int> intData(42);
        std::cout << "PropertyData<int> created with value: " << intData.valueBypassingBindings() << std::endl;
        
        // 测试setValue
        intData.setValueBypassingBindings(100);
        std::cout << "After setValue: " << intData.valueBypassingBindings() << std::endl;
        
        // 测试隐式转换
        int value = intData;
        std::cout << "Implicit conversion: " << value << std::endl;
        
        // 测试赋值
        intData = 200;
        std::cout << "After assignment: " << intData.valueBypassingBindings() << std::endl;
        
        // 测试比较
        PropertySystem::Core::PropertyData<int> intData2(200);
        if (intData == intData2) {
            std::cout << "Comparison works correctly" << std::endl;
        }
        
        std::cout << "All tests passed!" << std::endl;
        return 0;
        
    } catch (const std::exception& e) {
        std::cerr << "Error: " << e.what() << std::endl;
        return 1;
    } catch (...) {
        std::cerr << "Unknown error occurred" << std::endl;
        return 1;
    }
}
