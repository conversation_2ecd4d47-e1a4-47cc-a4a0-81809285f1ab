/**
 * @file Core/Property.cpp
 * @brief Property核心类实现
 * 
 * 这个文件提供了Property系统核心类的基础实现。
 */

#include "Property.h"
#include "PropertyBindingData.h"
#include "PropertyBindingPrivate.h"
#include "BindingEvaluationState.h"
#include "UntypedPropertyBinding.h"
#include "../Observer/PropertyObserver.h"

namespace PropertySystem::Core {

// ==================================================================================
// 全局状态管理
// ==================================================================================

namespace {
    thread_local BindingStatus* g_binding_status = nullptr;
    
    BindingStatus& getOrCreateBindingStatus() {
        if (!g_binding_status) {
            static thread_local BindingStatus status;
            g_binding_status = &status;
        }
        return *g_binding_status;
    }
}

// ==================================================================================
// 绑定状态管理函数实现
// ==================================================================================

namespace Internal {

BindingStatus* getBindingStatus() {
    return &getOrCreateBindingStatus();
}

void initBindingStatusThreadId() {
    getOrCreateBindingStatus().reinitAfterThreadMove();
}

bool isAnyBindingEvaluating() {
    auto* status = getBindingStatus();
    return status && status->currently_evaluating_binding != nullptr;
}

BindingEvaluationState* suspendCurrentBindingStatus() {
    auto* status = getBindingStatus();
    if (!status) return nullptr;
    
    auto* current = status->currently_evaluating_binding;
    status->currently_evaluating_binding = nullptr;
    return current;
}

void restoreBindingStatus(BindingEvaluationState *state) {
    auto* status = getBindingStatus();
    if (status) {
        status->currently_evaluating_binding = state;
    }
}

BindingStatus* getBindingStatus(BindingStatusAccessToken) {
    return getBindingStatus();
}

} // namespace Internal

// ==================================================================================
// BindingEvaluationState实现
// ==================================================================================

BindingEvaluationState::BindingEvaluationState(PropertyBindingPrivate *binding, BindingStatus *status)
    : binding(binding) {
    if (status) {
        previous_state_ = status->currently_evaluating_binding;
        current_state_ = &status->currently_evaluating_binding;
        status->currently_evaluating_binding = this;
    }
}

bool BindingEvaluationState::hasCircularDependency(const PropertyBindingData *property) const {
    // 检查是否已经在依赖列表中
    return isPropertyCaptured(property);
}

bool BindingEvaluationState::hasCircularBindingDependency(PropertyBindingPrivate *binding_to_check) const {
    // 遍历绑定链检查循环
    auto* current = this;
    while (current) {
        if (current->binding == binding_to_check) {
            return true;
        }
        current = current->previous_state_;
    }
    return false;
}

// ==================================================================================
// PropertyBindingError实现
// ==================================================================================

PropertyBindingError::PropertyBindingError() = default;

PropertyBindingError::PropertyBindingError(Type type, const std::string& description) {
    if (type != NoError) {
        d_ = std::make_shared<ErrorData>(type, description);
    }
}

PropertyBindingError::PropertyBindingError(const PropertyBindingError &other) = default;
PropertyBindingError::PropertyBindingError(PropertyBindingError &&other) noexcept = default;
PropertyBindingError &PropertyBindingError::operator=(const PropertyBindingError &other) = default;
PropertyBindingError &PropertyBindingError::operator=(PropertyBindingError &&other) noexcept = default;
PropertyBindingError::~PropertyBindingError() = default;

std::string PropertyBindingError::toString() const {
    if (!hasError()) {
        return "No error";
    }
    return typeToString(type()) + ": " + description();
}

void PropertyBindingError::ensureErrorData() {
    if (!d_) {
        d_ = std::make_shared<ErrorData>();
    }
}

// ==================================================================================
// UntypedPropertyBinding实现
// ==================================================================================

UntypedPropertyBinding::UntypedPropertyBinding() = default;

UntypedPropertyBinding::UntypedPropertyBinding(const Binding::ModernBindingFunctionVTable *vtable, 
                                              void *function, 
                                              const PropertyBindingSourceLocation &location) {
    // 创建绑定私有数据
    d_ = std::make_shared<Internal::PropertyBindingPrivate>(vtable, location);
    
    // 这里需要复制绑定函数到私有数据中
    // 具体实现取决于绑定函数的存储方式
}

UntypedPropertyBinding::UntypedPropertyBinding(const UntypedPropertyBinding &other) = default;
UntypedPropertyBinding::UntypedPropertyBinding(UntypedPropertyBinding &&other) noexcept = default;
UntypedPropertyBinding::~UntypedPropertyBinding() = default;
UntypedPropertyBinding &UntypedPropertyBinding::operator=(const UntypedPropertyBinding &other) = default;
UntypedPropertyBinding &UntypedPropertyBinding::operator=(UntypedPropertyBinding &&other) noexcept = default;

UntypedPropertyBinding::UntypedPropertyBinding(Internal::PropertyBindingPrivate *priv) 
    : d_(priv, [](Internal::PropertyBindingPrivate* p) { 
        if (p && !p->deref()) {
            Internal::PropertyBindingPrivate::destroyAndFreeMemory(p);
        }
    }) {}

bool UntypedPropertyBinding::isNull() const {
    return !d_;
}

bool UntypedPropertyBinding::isValid() const {
    return d_ && !d_->bindingError().hasError();
}

PropertyBindingError UntypedPropertyBinding::error() const {
    return d_ ? d_->bindingError() : PropertyBindingError();
}

PropertyBindingSourceLocation UntypedPropertyBinding::sourceLocation() const {
    return d_ ? d_->sourceLocation() : PropertyBindingSourceLocation();
}

void UntypedPropertyBinding::swap(UntypedPropertyBinding &other) noexcept {
    d_.swap(other.d_);
}

void UntypedPropertyBinding::reset() {
    d_.reset();
}

bool UntypedPropertyBinding::operator==(const UntypedPropertyBinding &other) const {
    return d_ == other.d_;
}

// ==================================================================================
// PropertyBindingPrivate基础实现
// ==================================================================================

namespace Internal {

PropertyBindingPrivate::PropertyBindingPrivate(const Binding::ModernBindingFunctionVTable *vtable,
                                              const PropertyBindingSourceLocation &location)
    : vtable_(vtable), location_(location) {}

PropertyBindingPrivate::~PropertyBindingPrivate() {
    clearDependencyObservers();
}

PropertyBindingError PropertyBindingPrivate::bindingError() const {
    return error_ ? *error_ : PropertyBindingError();
}

void PropertyBindingPrivate::setError(PropertyBindingError &&error) {
    if (error.hasError()) {
        error_ = std::make_unique<PropertyBindingError>(std::move(error));
    } else {
        error_.reset();
    }
}

void PropertyBindingPrivate::clearDependencyObservers() {
    dependency_observer_count_ = 0;
    heap_observers_.reset();
}

PropertyObserver* PropertyBindingPrivate::allocateDependencyObserver() {
    if (dependency_observer_count_ < inline_dependency_observers_.size()) {
        return &inline_dependency_observers_[dependency_observer_count_++];
    }
    return allocateDependencyObserver_slow();
}

PropertyObserver* PropertyBindingPrivate::allocateDependencyObserver_slow() {
    if (!heap_observers_) {
        heap_observers_ = std::make_unique<HeapObservers>();
    }
    heap_observers_->emplace_back();
    ++dependency_observer_count_;
    return &heap_observers_->back();
}

void PropertyBindingPrivate::detachFromProperty() {
    property_data_ptr_ = nullptr;
    clearDependencyObservers();
}

void PropertyBindingPrivate::unlinkAndDeref() {
    detachFromProperty();
    // 引用计数由智能指针管理
}

PropertyBindingPrivate* PropertyBindingPrivate::currentlyEvaluatingBinding() {
    auto* status = getBindingStatus();
    return status && status->currently_evaluating_binding ? 
           status->currently_evaluating_binding->binding : nullptr;
}

void PropertyBindingPrivate::destroyAndFreeMemory(PropertyBindingPrivate *priv) {
    delete priv;
}

} // namespace Internal

} // namespace PropertySystem::Core
