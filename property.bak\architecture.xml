<svg aria-roledescription="class" role="graphics-document document" viewBox="0 0 1579.2625732421875 958" style="max-width: 1579.2625732421875px;" class="classDiagram" xmlns:xlink="http://www.w3.org/1999/xlink" xmlns="http://www.w3.org/2000/svg" width="100%" id="mermaid-e2cc9d59-1fd2-422b-94d2-0c7648e79632"><style>#mermaid-e2cc9d59-1fd2-422b-94d2-0c7648e79632{font-family:"trebuchet ms",verdana,arial,sans-serif;font-size:16px;fill:#ccc;}#mermaid-e2cc9d59-1fd2-422b-94d2-0c7648e79632 .error-icon{fill:#a44141;}#mermaid-e2cc9d59-1fd2-422b-94d2-0c7648e79632 .error-text{fill:#ddd;stroke:#ddd;}#mermaid-e2cc9d59-1fd2-422b-94d2-0c7648e79632 .edge-thickness-normal{stroke-width:1px;}#mermaid-e2cc9d59-1fd2-422b-94d2-0c7648e79632 .edge-thickness-thick{stroke-width:3.5px;}#mermaid-e2cc9d59-1fd2-422b-94d2-0c7648e79632 .edge-pattern-solid{stroke-dasharray:0;}#mermaid-e2cc9d59-1fd2-422b-94d2-0c7648e79632 .edge-thickness-invisible{stroke-width:0;fill:none;}#mermaid-e2cc9d59-1fd2-422b-94d2-0c7648e79632 .edge-pattern-dashed{stroke-dasharray:3;}#mermaid-e2cc9d59-1fd2-422b-94d2-0c7648e79632 .edge-pattern-dotted{stroke-dasharray:2;}#mermaid-e2cc9d59-1fd2-422b-94d2-0c7648e79632 .marker{fill:lightgrey;stroke:lightgrey;}#mermaid-e2cc9d59-1fd2-422b-94d2-0c7648e79632 .marker.cross{stroke:lightgrey;}#mermaid-e2cc9d59-1fd2-422b-94d2-0c7648e79632 svg{font-family:"trebuchet ms",verdana,arial,sans-serif;font-size:16px;}#mermaid-e2cc9d59-1fd2-422b-94d2-0c7648e79632 p{margin:0;}#mermaid-e2cc9d59-1fd2-422b-94d2-0c7648e79632 g.classGroup text{fill:#ccc;stroke:none;font-family:"trebuchet ms",verdana,arial,sans-serif;font-size:10px;}#mermaid-e2cc9d59-1fd2-422b-94d2-0c7648e79632 g.classGroup text .title{font-weight:bolder;}#mermaid-e2cc9d59-1fd2-422b-94d2-0c7648e79632 .nodeLabel,#mermaid-e2cc9d59-1fd2-422b-94d2-0c7648e79632 .edgeLabel{color:#e0dfdf;}#mermaid-e2cc9d59-1fd2-422b-94d2-0c7648e79632 .edgeLabel .label rect{fill:#1f2020;}#mermaid-e2cc9d59-1fd2-422b-94d2-0c7648e79632 .label text{fill:#e0dfdf;}#mermaid-e2cc9d59-1fd2-422b-94d2-0c7648e79632 .labelBkg{background:#1f2020;}#mermaid-e2cc9d59-1fd2-422b-94d2-0c7648e79632 .edgeLabel .label span{background:#1f2020;}#mermaid-e2cc9d59-1fd2-422b-94d2-0c7648e79632 .classTitle{font-weight:bolder;}#mermaid-e2cc9d59-1fd2-422b-94d2-0c7648e79632 .node rect,#mermaid-e2cc9d59-1fd2-422b-94d2-0c7648e79632 .node circle,#mermaid-e2cc9d59-1fd2-422b-94d2-0c7648e79632 .node ellipse,#mermaid-e2cc9d59-1fd2-422b-94d2-0c7648e79632 .node polygon,#mermaid-e2cc9d59-1fd2-422b-94d2-0c7648e79632 .node path{fill:#1f2020;stroke:#ccc;stroke-width:1px;}#mermaid-e2cc9d59-1fd2-422b-94d2-0c7648e79632 .divider{stroke:#ccc;stroke-width:1;}#mermaid-e2cc9d59-1fd2-422b-94d2-0c7648e79632 g.clickable{cursor:pointer;}#mermaid-e2cc9d59-1fd2-422b-94d2-0c7648e79632 g.classGroup rect{fill:#1f2020;stroke:#ccc;}#mermaid-e2cc9d59-1fd2-422b-94d2-0c7648e79632 g.classGroup line{stroke:#ccc;stroke-width:1;}#mermaid-e2cc9d59-1fd2-422b-94d2-0c7648e79632 .classLabel .box{stroke:none;stroke-width:0;fill:#1f2020;opacity:0.5;}#mermaid-e2cc9d59-1fd2-422b-94d2-0c7648e79632 .classLabel .label{fill:#ccc;font-size:10px;}#mermaid-e2cc9d59-1fd2-422b-94d2-0c7648e79632 .relation{stroke:lightgrey;stroke-width:1;fill:none;}#mermaid-e2cc9d59-1fd2-422b-94d2-0c7648e79632 .dashed-line{stroke-dasharray:3;}#mermaid-e2cc9d59-1fd2-422b-94d2-0c7648e79632 .dotted-line{stroke-dasharray:1 2;}#mermaid-e2cc9d59-1fd2-422b-94d2-0c7648e79632 #compositionStart,#mermaid-e2cc9d59-1fd2-422b-94d2-0c7648e79632 .composition{fill:lightgrey!important;stroke:lightgrey!important;stroke-width:1;}#mermaid-e2cc9d59-1fd2-422b-94d2-0c7648e79632 #compositionEnd,#mermaid-e2cc9d59-1fd2-422b-94d2-0c7648e79632 .composition{fill:lightgrey!important;stroke:lightgrey!important;stroke-width:1;}#mermaid-e2cc9d59-1fd2-422b-94d2-0c7648e79632 #dependencyStart,#mermaid-e2cc9d59-1fd2-422b-94d2-0c7648e79632 .dependency{fill:lightgrey!important;stroke:lightgrey!important;stroke-width:1;}#mermaid-e2cc9d59-1fd2-422b-94d2-0c7648e79632 #dependencyStart,#mermaid-e2cc9d59-1fd2-422b-94d2-0c7648e79632 .dependency{fill:lightgrey!important;stroke:lightgrey!important;stroke-width:1;}#mermaid-e2cc9d59-1fd2-422b-94d2-0c7648e79632 #extensionStart,#mermaid-e2cc9d59-1fd2-422b-94d2-0c7648e79632 .extension{fill:transparent!important;stroke:lightgrey!important;stroke-width:1;}#mermaid-e2cc9d59-1fd2-422b-94d2-0c7648e79632 #extensionEnd,#mermaid-e2cc9d59-1fd2-422b-94d2-0c7648e79632 .extension{fill:transparent!important;stroke:lightgrey!important;stroke-width:1;}#mermaid-e2cc9d59-1fd2-422b-94d2-0c7648e79632 #aggregationStart,#mermaid-e2cc9d59-1fd2-422b-94d2-0c7648e79632 .aggregation{fill:transparent!important;stroke:lightgrey!important;stroke-width:1;}#mermaid-e2cc9d59-1fd2-422b-94d2-0c7648e79632 #aggregationEnd,#mermaid-e2cc9d59-1fd2-422b-94d2-0c7648e79632 .aggregation{fill:transparent!important;stroke:lightgrey!important;stroke-width:1;}#mermaid-e2cc9d59-1fd2-422b-94d2-0c7648e79632 #lollipopStart,#mermaid-e2cc9d59-1fd2-422b-94d2-0c7648e79632 .lollipop{fill:#1f2020!important;stroke:lightgrey!important;stroke-width:1;}#mermaid-e2cc9d59-1fd2-422b-94d2-0c7648e79632 #lollipopEnd,#mermaid-e2cc9d59-1fd2-422b-94d2-0c7648e79632 .lollipop{fill:#1f2020!important;stroke:lightgrey!important;stroke-width:1;}#mermaid-e2cc9d59-1fd2-422b-94d2-0c7648e79632 .edgeTerminals{font-size:11px;line-height:initial;}#mermaid-e2cc9d59-1fd2-422b-94d2-0c7648e79632 .classTitleText{text-anchor:middle;font-size:18px;fill:#ccc;}#mermaid-e2cc9d59-1fd2-422b-94d2-0c7648e79632 :root{--mermaid-font-family:"trebuchet ms",verdana,arial,sans-serif;}</style><g><defs><marker orient="auto" markerHeight="240" markerWidth="190" refY="7" refX="18" class="marker aggregation class" id="mermaid-e2cc9d59-1fd2-422b-94d2-0c7648e79632_class-aggregationStart"><path d="M 18,7 L9,13 L1,7 L9,1 Z"></path></marker></defs><defs><marker orient="auto" markerHeight="28" markerWidth="20" refY="7" refX="1" class="marker aggregation class" id="mermaid-e2cc9d59-1fd2-422b-94d2-0c7648e79632_class-aggregationEnd"><path d="M 18,7 L9,13 L1,7 L9,1 Z"></path></marker></defs><defs><marker orient="auto" markerHeight="240" markerWidth="190" refY="7" refX="18" class="marker extension class" id="mermaid-e2cc9d59-1fd2-422b-94d2-0c7648e79632_class-extensionStart"><path d="M 1,7 L18,13 V 1 Z"></path></marker></defs><defs><marker orient="auto" markerHeight="28" markerWidth="20" refY="7" refX="1" class="marker extension class" id="mermaid-e2cc9d59-1fd2-422b-94d2-0c7648e79632_class-extensionEnd"><path d="M 1,1 V 13 L18,7 Z"></path></marker></defs><defs><marker orient="auto" markerHeight="240" markerWidth="190" refY="7" refX="18" class="marker composition class" id="mermaid-e2cc9d59-1fd2-422b-94d2-0c7648e79632_class-compositionStart"><path d="M 18,7 L9,13 L1,7 L9,1 Z"></path></marker></defs><defs><marker orient="auto" markerHeight="28" markerWidth="20" refY="7" refX="1" class="marker composition class" id="mermaid-e2cc9d59-1fd2-422b-94d2-0c7648e79632_class-compositionEnd"><path d="M 18,7 L9,13 L1,7 L9,1 Z"></path></marker></defs><defs><marker orient="auto" markerHeight="240" markerWidth="190" refY="7" refX="6" class="marker dependency class" id="mermaid-e2cc9d59-1fd2-422b-94d2-0c7648e79632_class-dependencyStart"><path d="M 5,7 L9,13 L1,7 L9,1 Z"></path></marker></defs><defs><marker orient="auto" markerHeight="28" markerWidth="20" refY="7" refX="13" class="marker dependency class" id="mermaid-e2cc9d59-1fd2-422b-94d2-0c7648e79632_class-dependencyEnd"><path d="M 18,7 L9,13 L14,7 L9,1 Z"></path></marker></defs><defs><marker orient="auto" markerHeight="240" markerWidth="190" refY="7" refX="13" class="marker lollipop class" id="mermaid-e2cc9d59-1fd2-422b-94d2-0c7648e79632_class-lollipopStart"><circle r="6" cy="7" cx="7" fill="transparent" stroke="black"></circle></marker></defs><defs><marker orient="auto" markerHeight="240" markerWidth="190" refY="7" refX="1" class="marker lollipop class" id="mermaid-e2cc9d59-1fd2-422b-94d2-0c7648e79632_class-lollipopEnd"><circle r="6" cy="7" cx="7" fill="transparent" stroke="black"></circle></marker></defs><g class="root"><g class="clusters"></g><g class="edgePaths"><path marker-start="url(#mermaid-e2cc9d59-1fd2-422b-94d2-0c7648e79632_class-extensionStart)" style=";" class="edge-thickness-normal edge-pattern-solid relation" id="id_UntypedPropertyData_PropertyData_1" d="M155.966,176L155.966,184.167C155.966,192.333,155.966,208.667,155.966,221C155.966,233.333,155.966,241.667,155.966,245.833L155.966,250"></path><path marker-start="url(#mermaid-e2cc9d59-1fd2-422b-94d2-0c7648e79632_class-extensionStart)" style=";" class="edge-thickness-normal edge-pattern-solid relation" id="id_PropertyData_Property_2" d="M155.966,436L155.966,437.167C155.966,438.333,155.966,440.667,155.966,446C155.966,451.333,155.966,459.667,155.966,463.833L155.966,468"></path><path marker-start="url(#mermaid-e2cc9d59-1fd2-422b-94d2-0c7648e79632_class-compositionStart)" style=";" class="edge-thickness-normal edge-pattern-solid relation" id="id_Property_PropertyBindingData_3" d="M155.966,726L155.966,727.167C155.966,728.333,155.966,730.667,155.966,736C155.966,741.333,155.966,749.667,155.966,753.833L155.966,758"></path><path marker-start="url(#mermaid-e2cc9d59-1fd2-422b-94d2-0c7648e79632_class-extensionStart)" style=";" class="edge-thickness-normal edge-pattern-solid relation" id="id_UntypedPropertyBinding_PropertyBinding_4" d="M514.989,211.207L512.1,213.506C509.21,215.805,503.432,220.402,500.542,230.368C497.653,240.333,497.653,255.667,497.653,263.333L497.653,271"></path><path marker-start="url(#mermaid-e2cc9d59-1fd2-422b-94d2-0c7648e79632_class-compositionStart)" style=";" class="edge-thickness-normal edge-pattern-solid relation" id="id_UntypedPropertyBinding_PropertyBindingPrivatePtr_5" d="M784.477,211.207L787.366,213.506C790.255,215.805,796.034,220.402,798.923,233.868C801.813,247.333,801.813,269.667,801.813,280.833L801.813,292"></path><path marker-end="url(#mermaid-e2cc9d59-1fd2-422b-94d2-0c7648e79632_class-dependencyEnd)" style=";" class="edge-thickness-normal edge-pattern-solid relation" id="id_PropertyBindingPrivatePtr_PropertyBindingPrivate_6" d="M801.813,376L801.813,387.167C801.813,398.333,801.813,420.667,801.813,437C801.813,453.333,801.813,463.667,801.813,468.833L801.813,474"></path><path marker-start="url(#mermaid-e2cc9d59-1fd2-422b-94d2-0c7648e79632_class-compositionStart)" style=";" class="edge-thickness-normal edge-pattern-solid relation" id="id_PropertyBindingPrivate_PropertyObserverPointer_7" d="M801.813,714L801.813,717.167C801.813,720.333,801.813,726.667,801.813,743C801.813,759.333,801.813,785.667,801.813,798.833L801.813,812"></path><path marker-start="url(#mermaid-e2cc9d59-1fd2-422b-94d2-0c7648e79632_class-extensionStart)" style=";" class="edge-thickness-normal edge-pattern-solid relation" id="id_PropertyObserver_PropertyChangeHandler_8" d="M1144.918,198.655L1138.94,203.046C1132.961,207.437,1121.004,216.218,1115.025,228.776C1109.047,241.333,1109.047,257.667,1109.047,265.833L1109.047,274"></path><path marker-start="url(#mermaid-e2cc9d59-1fd2-422b-94d2-0c7648e79632_class-extensionStart)" style=";" class="edge-thickness-normal edge-pattern-solid relation" id="id_PropertyObserver_PropertyNotifier_9" d="M1402.682,198.655L1408.66,203.046C1414.639,207.437,1426.596,216.218,1432.575,228.776C1438.553,241.333,1438.553,257.667,1438.553,265.833L1438.553,274"></path></g><g class="edgeLabels"><g class="edgeLabel"><g transform="translate(0, -12)" class="label"><foreignObject height="24" width="0"><div class="labelBkg" xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="edgeLabel" style=";display: inline-block"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, -12)" class="label"><foreignObject height="24" width="0"><div class="labelBkg" xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="edgeLabel" style=";display: inline-block"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, -12)" class="label"><foreignObject height="24" width="0"><div class="labelBkg" xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="edgeLabel" style=";display: inline-block"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, -12)" class="label"><foreignObject height="24" width="0"><div class="labelBkg" xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="edgeLabel" style=";display: inline-block"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, -12)" class="label"><foreignObject height="24" width="0"><div class="labelBkg" xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="edgeLabel" style=";display: inline-block"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, -12)" class="label"><foreignObject height="24" width="0"><div class="labelBkg" xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="edgeLabel" style=";display: inline-block"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, -12)" class="label"><foreignObject height="24" width="0"><div class="labelBkg" xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="edgeLabel" style=";display: inline-block"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, -12)" class="label"><foreignObject height="24" width="0"><div class="labelBkg" xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="edgeLabel" style=";display: inline-block"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, -12)" class="label"><foreignObject height="24" width="0"><div class="labelBkg" xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="edgeLabel" style=";display: inline-block"></span></div></foreignObject></g></g></g><g class="nodes"><g transform="translate(155.9656219482422, 104)" id="classId-UntypedPropertyData-0" class="node default"><g class="basic label-container"><path style="" fill="#1f2020" stroke-width="0" stroke="none" d="M-93.0875015258789 -54 L93.0875015258789 -54 L93.0875015258789 54 L-93.0875015258789 54"></path><path style="" fill="none" stroke-width="1.3" stroke="#ccc" d="M-93.0875015258789 -54 C-26.20031039175346 -54, 40.68688074237198 -54, 93.0875015258789 -54 M-93.0875015258789 -54 C-41.65812632904525 -54, 9.771248867788401 -54, 93.0875015258789 -54 M93.0875015258789 -54 C93.0875015258789 -23.365833536445358, 93.0875015258789 7.2683329271092845, 93.0875015258789 54 M93.0875015258789 -54 C93.0875015258789 -18.961980221880047, 93.0875015258789 16.076039556239905, 93.0875015258789 54 M93.0875015258789 54 C36.721482375996914 54, -19.64453677388508 54, -93.0875015258789 54 M93.0875015258789 54 C46.67468395386738 54, 0.2618663818558531 54, -93.0875015258789 54 M-93.0875015258789 54 C-93.0875015258789 31.01356353367194, -93.0875015258789 8.027127067343883, -93.0875015258789 -54 M-93.0875015258789 54 C-93.0875015258789 31.56559805384202, -93.0875015258789 9.13119610768404, -93.0875015258789 -54"></path></g><g transform="translate(-37.90625, -30)" class="annotation-group text"><g transform="translate(0,-12)" style="" class="label"><foreignObject height="24" width="75.8125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 126px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>«abstract»</p></span></div></foreignObject></g></g><g transform="translate(-81.0875015258789, -6)" class="label-group text"><g transform="translate(0,-12)" style="font-weight: bolder" class="label"><foreignObject height="24" width="162.1750030517578"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 214px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>UntypedPropertyData</p></span></div></foreignObject></g></g><g transform="translate(-81.0875015258789, 42)" class="members-group text"></g><g transform="translate(-81.0875015258789, 72)" class="methods-group text"></g><g style="" class="divider"><path style="" fill="none" stroke-width="1.3" stroke="#ccc" d="M-93.0875015258789 18 C-29.876362369088817 18, 33.33477678770127 18, 93.0875015258789 18 M-93.0875015258789 18 C-26.741678754660654 18, 39.6041440165576 18, 93.0875015258789 18"></path></g><g style="" class="divider"><path style="" fill="none" stroke-width="1.3" stroke="#ccc" d="M-93.0875015258789 36 C-53.56876478535202 36, -14.05002804482514 36, 93.0875015258789 36 M-93.0875015258789 36 C-24.82688485673914 36, 43.43373181240062 36, 93.0875015258789 36"></path></g></g><g transform="translate(155.9656219482422, 334)" id="classId-PropertyData-1" class="node default"><g class="basic label-container"><path style="" fill="#1f2020" stroke-width="0" stroke="none" d="M-147.9656219482422 -84 L147.9656219482422 -84 L147.9656219482422 84 L-147.9656219482422 84"></path><path style="" fill="none" stroke-width="1.3" stroke="#ccc" d="M-147.9656219482422 -84 C-37.50983161138714 -84, 72.9459587254679 -84, 147.9656219482422 -84 M-147.9656219482422 -84 C-56.4888095352142 -84, 34.988002877813784 -84, 147.9656219482422 -84 M147.9656219482422 -84 C147.9656219482422 -22.29583007386435, 147.9656219482422 39.4083398522713, 147.9656219482422 84 M147.9656219482422 -84 C147.9656219482422 -18.282947786957322, 147.9656219482422 47.434104426085355, 147.9656219482422 84 M147.9656219482422 84 C78.86115996887925 84, 9.756697989516312 84, -147.9656219482422 84 M147.9656219482422 84 C64.00878378478363 84, -19.948054378674925 84, -147.9656219482422 84 M-147.9656219482422 84 C-147.9656219482422 25.893585361472326, -147.9656219482422 -32.21282927705535, -147.9656219482422 -84 M-147.9656219482422 84 C-147.9656219482422 45.0043555033888, -147.9656219482422 6.008711006777602, -147.9656219482422 -84"></path></g><g transform="translate(0, -60)" class="annotation-group text"></g><g transform="translate(-63.868751525878906, -60)" class="label-group text"><g transform="translate(0,-12)" style="font-weight: bolder" class="label"><foreignObject height="24" width="127.73750305175781"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 216px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>PropertyData&lt;T&gt;</p></span></div></foreignObject></g></g><g transform="translate(-135.9656219482422, -12)" class="members-group text"><g transform="translate(0,-12)" style="" class="label"><foreignObject height="24" width="40.66250228881836"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 96px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>-T val</p></span></div></foreignObject></g></g><g transform="translate(-135.9656219482422, 36)" class="methods-group text"><g transform="translate(0,-12)" style="" class="label"><foreignObject height="24" width="186.21250915527344"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 257px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+valueBypassingBindings()</p></span></div></foreignObject></g><g transform="translate(0,12)" style="" class="label"><foreignObject height="24" width="208.0625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 280px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+setValueBypassingBindings()</p></span></div></foreignObject></g></g><g style="" class="divider"><path style="" fill="none" stroke-width="1.3" stroke="#ccc" d="M-147.9656219482422 -36 C-85.13315259269498 -36, -22.30068323714778 -36, 147.9656219482422 -36 M-147.9656219482422 -36 C-66.48074127293712 -36, 15.00413940236794 -36, 147.9656219482422 -36"></path></g><g style="" class="divider"><path style="" fill="none" stroke-width="1.3" stroke="#ccc" d="M-147.9656219482422 12 C-57.00686491039076 12, 33.95189212746067 12, 147.9656219482422 12 M-147.9656219482422 12 C-55.32095614267159 12, 37.323709662899006 12, 147.9656219482422 12"></path></g></g><g transform="translate(155.9656219482422, 588)" id="classId-Property-2" class="node default"><g class="basic label-container"><path style="" fill="#1f2020" stroke-width="0" stroke="none" d="M-118.6468734741211 -120 L118.6468734741211 -120 L118.6468734741211 120 L-118.6468734741211 120"></path><path style="" fill="none" stroke-width="1.3" stroke="#ccc" d="M-118.6468734741211 -120 C-56.35160725551377 -120, 5.943658963093554 -120, 118.6468734741211 -120 M-118.6468734741211 -120 C-52.32549228799634 -120, 13.995888898128413 -120, 118.6468734741211 -120 M118.6468734741211 -120 C118.6468734741211 -69.30545313949636, 118.6468734741211 -18.61090627899273, 118.6468734741211 120 M118.6468734741211 -120 C118.6468734741211 -68.25209454209914, 118.6468734741211 -16.504189084198273, 118.6468734741211 120 M118.6468734741211 120 C67.016834304095 120, 15.386795134068905 120, -118.6468734741211 120 M118.6468734741211 120 C28.730815694217057 120, -61.18524208568698 120, -118.6468734741211 120 M-118.6468734741211 120 C-118.6468734741211 29.789139733461155, -118.6468734741211 -60.42172053307769, -118.6468734741211 -120 M-118.6468734741211 120 C-118.6468734741211 54.95499902531404, -118.6468734741211 -10.090001949371924, -118.6468734741211 -120"></path></g><g transform="translate(0, -96)" class="annotation-group text"></g><g transform="translate(-47.03125, -96)" class="label-group text"><g transform="translate(0,-12)" style="font-weight: bolder" class="label"><foreignObject height="24" width="94.0625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 181px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>Property&lt;T&gt;</p></span></div></foreignObject></g></g><g transform="translate(-106.6468734741211, -48)" class="members-group text"><g transform="translate(0,-12)" style="" class="label"><foreignObject height="24" width="166.2624969482422"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 233px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>-PropertyBindingData d</p></span></div></foreignObject></g></g><g transform="translate(-106.6468734741211, 0)" class="methods-group text"><g transform="translate(0,-12)" style="" class="label"><foreignObject height="24" width="58.57500076293945"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 116px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+value()</p></span></div></foreignObject></g><g transform="translate(0,12)" style="" class="label"><foreignObject height="24" width="80.42500305175781"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 139px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+setValue()</p></span></div></foreignObject></g><g transform="translate(0,36)" style="" class="label"><foreignObject height="24" width="94.30000305175781"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 156px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+setBinding()</p></span></div></foreignObject></g><g transform="translate(0,60)" style="" class="label"><foreignObject height="24" width="96.375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 160px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+hasBinding()</p></span></div></foreignObject></g><g transform="translate(0,84)" style="" class="label"><foreignObject height="24" width="137.33750915527344"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 202px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+onValueChanged()</p></span></div></foreignObject></g></g><g style="" class="divider"><path style="" fill="none" stroke-width="1.3" stroke="#ccc" d="M-118.6468734741211 -72 C-69.04297496171813 -72, -19.439076449315152 -72, 118.6468734741211 -72 M-118.6468734741211 -72 C-51.91136855957096 -72, 14.824136354979174 -72, 118.6468734741211 -72"></path></g><g style="" class="divider"><path style="" fill="none" stroke-width="1.3" stroke="#ccc" d="M-118.6468734741211 -24 C-52.98292130545617 -24, 12.681030863208747 -24, 118.6468734741211 -24 M-118.6468734741211 -24 C-61.08847404665014 -24, -3.5300746191791887 -24, 118.6468734741211 -24"></path></g></g><g transform="translate(155.9656219482422, 854)" id="classId-PropertyBindingData-3" class="node default"><g class="basic label-container"><path style="" fill="#1f2020" stroke-width="0" stroke="none" d="M-116.9000015258789 -96 L116.9000015258789 -96 L116.9000015258789 96 L-116.9000015258789 96"></path><path style="" fill="none" stroke-width="1.3" stroke="#ccc" d="M-116.9000015258789 -96 C-64.39994303660899 -96, -11.899884547339084 -96, 116.9000015258789 -96 M-116.9000015258789 -96 C-53.080804599621516 -96, 10.738392326635875 -96, 116.9000015258789 -96 M116.9000015258789 -96 C116.9000015258789 -23.12311246751875, 116.9000015258789 49.7537750649625, 116.9000015258789 96 M116.9000015258789 -96 C116.9000015258789 -29.64041450054613, 116.9000015258789 36.71917099890774, 116.9000015258789 96 M116.9000015258789 96 C54.97332993062462 96, -6.9533416646296615 96, -116.9000015258789 96 M116.9000015258789 96 C44.72317838049037 96, -27.453644764898172 96, -116.9000015258789 96 M-116.9000015258789 96 C-116.9000015258789 30.334911751017813, -116.9000015258789 -35.33017649796437, -116.9000015258789 -96 M-116.9000015258789 96 C-116.9000015258789 21.109027134312697, -116.9000015258789 -53.781945731374606, -116.9000015258789 -96"></path></g><g transform="translate(0, -72)" class="annotation-group text"></g><g transform="translate(-77.23750305175781, -72)" class="label-group text"><g transform="translate(0,-12)" style="font-weight: bolder" class="label"><foreignObject height="24" width="154.47500610351562"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 207px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>PropertyBindingData</p></span></div></foreignObject></g></g><g transform="translate(-104.9000015258789, -24)" class="members-group text"><g transform="translate(0,-12)" style="" class="label"><foreignObject height="24" width="114.0875015258789"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 174px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>-uintptr_t d_ptr</p></span></div></foreignObject></g></g><g transform="translate(-104.9000015258789, 24)" class="methods-group text"><g transform="translate(0,-12)" style="" class="label"><foreignObject height="24" width="96.375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 160px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+hasBinding()</p></span></div></foreignObject></g><g transform="translate(0,12)" style="" class="label"><foreignObject height="24" width="94.30000305175781"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 156px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+setBinding()</p></span></div></foreignObject></g><g transform="translate(0,36)" style="" class="label"><foreignObject height="24" width="132.5625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 194px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+notifyObservers()</p></span></div></foreignObject></g></g><g style="" class="divider"><path style="" fill="none" stroke-width="1.3" stroke="#ccc" d="M-116.9000015258789 -48 C-53.01449532390717 -48, 10.871010878064567 -48, 116.9000015258789 -48 M-116.9000015258789 -48 C-47.277184092850376 -48, 22.345633340178154 -48, 116.9000015258789 -48"></path></g><g style="" class="divider"><path style="" fill="none" stroke-width="1.3" stroke="#ccc" d="M-116.9000015258789 0 C-44.55117410028515 0, 27.797653325308602 0, 116.9000015258789 0 M-116.9000015258789 0 C-54.495021745751764 0, 7.909958034375379 0, 116.9000015258789 0"></path></g></g><g transform="translate(649.7328338623047, 104)" id="classId-UntypedPropertyBinding-4" class="node default"><g class="basic label-container"><path style="" fill="#1f2020" stroke-width="0" stroke="none" d="M-160.46876525878906 -96 L160.46876525878906 -96 L160.46876525878906 96 L-160.46876525878906 96"></path><path style="" fill="none" stroke-width="1.3" stroke="#ccc" d="M-160.46876525878906 -96 C-79.12353548114702 -96, 2.2216942964950306 -96, 160.46876525878906 -96 M-160.46876525878906 -96 C-72.3290408452473 -96, 15.81068356829445 -96, 160.46876525878906 -96 M160.46876525878906 -96 C160.46876525878906 -26.562794124302457, 160.46876525878906 42.87441175139509, 160.46876525878906 96 M160.46876525878906 -96 C160.46876525878906 -42.59479286576319, 160.46876525878906 10.810414268473622, 160.46876525878906 96 M160.46876525878906 96 C59.14421764218608 96, -42.18032997441691 96, -160.46876525878906 96 M160.46876525878906 96 C64.92891128870839 96, -30.61094268137228 96, -160.46876525878906 96 M-160.46876525878906 96 C-160.46876525878906 23.86590273450811, -160.46876525878906 -48.26819453098378, -160.46876525878906 -96 M-160.46876525878906 96 C-160.46876525878906 38.14278612488576, -160.46876525878906 -19.71442775022848, -160.46876525878906 -96"></path></g><g transform="translate(0, -72)" class="annotation-group text"></g><g transform="translate(-91.88750457763672, -72)" class="label-group text"><g transform="translate(0,-12)" style="font-weight: bolder" class="label"><foreignObject height="24" width="183.77500915527344"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 238px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>UntypedPropertyBinding</p></span></div></foreignObject></g></g><g transform="translate(-148.46876525878906, -24)" class="members-group text"><g transform="translate(0,-12)" style="" class="label"><foreignObject height="24" width="205.0500030517578"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 273px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>-PropertyBindingPrivatePtr d</p></span></div></foreignObject></g></g><g transform="translate(-148.46876525878906, 24)" class="methods-group text"><g transform="translate(0,-12)" style="" class="label"><foreignObject height="24" width="59.57500076293945"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 118px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+isNull()</p></span></div></foreignObject></g><g transform="translate(0,12)" style="" class="label"><foreignObject height="24" width="56.11249923706055"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 113px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+error()</p></span></div></foreignObject></g><g transform="translate(0,36)" style="" class="label"><foreignObject height="24" width="126.375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 188px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+valueMetaType()</p></span></div></foreignObject></g></g><g style="" class="divider"><path style="" fill="none" stroke-width="1.3" stroke="#ccc" d="M-160.46876525878906 -48 C-54.16479527778242 -48, 52.13917470322423 -48, 160.46876525878906 -48 M-160.46876525878906 -48 C-33.36194201007011 -48, 93.74488123864884 -48, 160.46876525878906 -48"></path></g><g style="" class="divider"><path style="" fill="none" stroke-width="1.3" stroke="#ccc" d="M-160.46876525878906 0 C-67.70114457525834 0, 25.066476108272383 0, 160.46876525878906 0 M-160.46876525878906 0 C-52.934039601578334 0, 54.600686055632394 0, 160.46876525878906 0"></path></g></g><g transform="translate(497.65313720703125, 334)" id="classId-PropertyBinding-5" class="node default"><g class="basic label-container"><path style="" fill="#1f2020" stroke-width="0" stroke="none" d="M-143.72189331054688 -63 L143.72189331054688 -63 L143.72189331054688 63 L-143.72189331054688 63"></path><path style="" fill="none" stroke-width="1.3" stroke="#ccc" d="M-143.72189331054688 -63 C-80.57049264139239 -63, -17.419091972237908 -63, 143.72189331054688 -63 M-143.72189331054688 -63 C-70.25551554617975 -63, 3.2108622181873727 -63, 143.72189331054688 -63 M143.72189331054688 -63 C143.72189331054688 -16.4249563495364, 143.72189331054688 30.150087300927197, 143.72189331054688 63 M143.72189331054688 -63 C143.72189331054688 -19.74510219589454, 143.72189331054688 23.509795608210922, 143.72189331054688 63 M143.72189331054688 63 C61.54690452010969 63, -20.62808427032749 63, -143.72189331054688 63 M143.72189331054688 63 C42.15715996209195 63, -59.40757338636297 63, -143.72189331054688 63 M-143.72189331054688 63 C-143.72189331054688 17.760261776310145, -143.72189331054688 -27.47947644737971, -143.72189331054688 -63 M-143.72189331054688 63 C-143.72189331054688 25.525741290576747, -143.72189331054688 -11.948517418846507, -143.72189331054688 -63"></path></g><g transform="translate(0, -39)" class="annotation-group text"></g><g transform="translate(-74.66875457763672, -39)" class="label-group text"><g transform="translate(0,-12)" style="font-weight: bolder" class="label"><foreignObject height="24" width="149.33750915527344"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 239px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>PropertyBinding&lt;T&gt;</p></span></div></foreignObject></g></g><g transform="translate(-131.72189331054688, 9)" class="members-group text"></g><g transform="translate(-131.72189331054688, 39)" class="methods-group text"><g transform="translate(0,-12)" style="" class="label"><foreignObject height="24" width="188.77500915527344"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 256px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+PropertyBinding(Functor)</p></span></div></foreignObject></g></g><g style="" class="divider"><path style="" fill="none" stroke-width="1.3" stroke="#ccc" d="M-143.72189331054688 -15 C-58.70547615341934 -15, 26.310941003708194 -15, 143.72189331054688 -15 M-143.72189331054688 -15 C-78.17913458193726 -15, -12.63637585332765 -15, 143.72189331054688 -15"></path></g><g style="" class="divider"><path style="" fill="none" stroke-width="1.3" stroke="#ccc" d="M-143.72189331054688 9 C-30.308762381516658 9, 83.10436854751356 9, 143.72189331054688 9 M-143.72189331054688 9 C-50.391927195369874 9, 42.93803891980713 9, 143.72189331054688 9"></path></g></g><g transform="translate(801.8125305175781, 588)" id="classId-PropertyBindingPrivate-6" class="node default"><g class="basic label-container"><path style="" fill="#1f2020" stroke-width="0" stroke="none" d="M-195.7937469482422 -108 L195.7937469482422 -108 L195.7937469482422 108 L-195.7937469482422 108"></path><path style="" fill="none" stroke-width="1.3" stroke="#ccc" d="M-195.7937469482422 -108 C-79.2758791570203 -108, 37.24198863420159 -108, 195.7937469482422 -108 M-195.7937469482422 -108 C-54.21439378091159 -108, 87.364959386419 -108, 195.7937469482422 -108 M195.7937469482422 -108 C195.7937469482422 -61.86676729893156, 195.7937469482422 -15.733534597863127, 195.7937469482422 108 M195.7937469482422 -108 C195.7937469482422 -27.642218439987374, 195.7937469482422 52.71556312002525, 195.7937469482422 108 M195.7937469482422 108 C102.36529495739452 108, 8.936842966546862 108, -195.7937469482422 108 M195.7937469482422 108 C115.7051853798481 108, 35.61662381145402 108, -195.7937469482422 108 M-195.7937469482422 108 C-195.7937469482422 23.75000386847094, -195.7937469482422 -60.49999226305812, -195.7937469482422 -108 M-195.7937469482422 108 C-195.7937469482422 27.977822247836542, -195.7937469482422 -52.044355504326916, -195.7937469482422 -108"></path></g><g transform="translate(0, -84)" class="annotation-group text"></g><g transform="translate(-87.1500015258789, -84)" class="label-group text"><g transform="translate(0,-12)" style="font-weight: bolder" class="label"><foreignObject height="24" width="174.3000030517578"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 225px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>PropertyBindingPrivate</p></span></div></foreignObject></g></g><g transform="translate(-183.7937469482422, -36)" class="members-group text"><g transform="translate(0,-12)" style="" class="label"><foreignObject height="24" width="223.625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 292px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>-BindingFunctionVTable* vtable</p></span></div></foreignObject></g><g transform="translate(0,12)" style="" class="label"><foreignObject height="24" width="280.4375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 351px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>-PropertyObserverPointer firstObserver</p></span></div></foreignObject></g><g transform="translate(0,36)" style="" class="label"><foreignObject height="24" width="148.2375030517578"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 211px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>-MetaType metaType</p></span></div></foreignObject></g></g><g transform="translate(-183.7937469482422, 60)" class="methods-group text"><g transform="translate(0,-12)" style="" class="label"><foreignObject height="24" width="149.9250030517578"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 211px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+evaluateRecursive()</p></span></div></foreignObject></g><g transform="translate(0,12)" style="" class="label"><foreignObject height="24" width="157.60000610351562"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 222px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+notifyNonRecursive()</p></span></div></foreignObject></g></g><g style="" class="divider"><path style="" fill="none" stroke-width="1.3" stroke="#ccc" d="M-195.7937469482422 -60 C-85.41657975241327 -60, 24.960587443415648 -60, 195.7937469482422 -60 M-195.7937469482422 -60 C-112.43926754030524 -60, -29.08478813236829 -60, 195.7937469482422 -60"></path></g><g style="" class="divider"><path style="" fill="none" stroke-width="1.3" stroke="#ccc" d="M-195.7937469482422 36 C-46.307790970304666 36, 103.17816500763286 36, 195.7937469482422 36 M-195.7937469482422 36 C-45.70201931243335 36, 104.38970832337549 36, 195.7937469482422 36"></path></g></g><g transform="translate(1273.800048828125, 104)" id="classId-PropertyObserver-7" class="node default"><g class="basic label-container"><path style="" fill="#1f2020" stroke-width="0" stroke="none" d="M-174.57814025878906 -84 L174.57814025878906 -84 L174.57814025878906 84 L-174.57814025878906 84"></path><path style="" fill="none" stroke-width="1.3" stroke="#ccc" d="M-174.57814025878906 -84 C-91.64998331011002 -84, -8.72182636143097 -84, 174.57814025878906 -84 M-174.57814025878906 -84 C-79.78201422067733 -84, 15.014111817434411 -84, 174.57814025878906 -84 M174.57814025878906 -84 C174.57814025878906 -38.48426154744618, 174.57814025878906 7.031476905107638, 174.57814025878906 84 M174.57814025878906 -84 C174.57814025878906 -31.954170543462673, 174.57814025878906 20.091658913074653, 174.57814025878906 84 M174.57814025878906 84 C36.6056679074924 84, -101.36680444380426 84, -174.57814025878906 84 M174.57814025878906 84 C86.16047427025036 84, -2.2571917182883396 84, -174.57814025878906 84 M-174.57814025878906 84 C-174.57814025878906 24.881384398167107, -174.57814025878906 -34.237231203665786, -174.57814025878906 -84 M-174.57814025878906 84 C-174.57814025878906 43.512349537889094, -174.57814025878906 3.0246990757781873, -174.57814025878906 -84"></path></g><g transform="translate(0, -60)" class="annotation-group text"></g><g transform="translate(-66.73125457763672, -60)" class="label-group text"><g transform="translate(0,-12)" style="font-weight: bolder" class="label"><foreignObject height="24" width="133.46250915527344"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 184px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>PropertyObserver</p></span></div></foreignObject></g></g><g transform="translate(-162.57814025878906, -12)" class="members-group text"><g transform="translate(0,-12)" style="" class="label"><foreignObject height="24" width="143.28750610351562"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 205px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>-TaggedPointer next</p></span></div></foreignObject></g><g transform="translate(0,12)" style="" class="label"><foreignObject height="24" width="258.4250183105469"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 331px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>-TagPreservingPointerToPointer prev</p></span></div></foreignObject></g></g><g transform="translate(-162.57814025878906, 60)" class="methods-group text"><g transform="translate(0,-12)" style="" class="label"><foreignObject height="24" width="89.5875015258789"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 150px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+setSource()</p></span></div></foreignObject></g></g><g style="" class="divider"><path style="" fill="none" stroke-width="1.3" stroke="#ccc" d="M-174.57814025878906 -36 C-45.660427233603144 -36, 83.25728579158277 -36, 174.57814025878906 -36 M-174.57814025878906 -36 C-71.39504101689927 -36, 31.78805822499052 -36, 174.57814025878906 -36"></path></g><g style="" class="divider"><path style="" fill="none" stroke-width="1.3" stroke="#ccc" d="M-174.57814025878906 36 C-67.49635495532402 36, 39.58543034814102 36, 174.57814025878906 36 M-174.57814025878906 36 C-101.35619888946461 36, -28.134257520140153 36, 174.57814025878906 36"></path></g></g><g transform="translate(1109.0469207763672, 334)" id="classId-PropertyChangeHandler-8" class="node default"><g class="basic label-container"><path style="" fill="#1f2020" stroke-width="0" stroke="none" d="M-146.79689025878906 -60 L146.79689025878906 -60 L146.79689025878906 60 L-146.79689025878906 60"></path><path style="" fill="none" stroke-width="1.3" stroke="#ccc" d="M-146.79689025878906 -60 C-84.14392127860667 -60, -21.490952298424276 -60, 146.79689025878906 -60 M-146.79689025878906 -60 C-46.3069168647188 -60, 54.18305652935146 -60, 146.79689025878906 -60 M146.79689025878906 -60 C146.79689025878906 -33.46688671499288, 146.79689025878906 -6.93377342998577, 146.79689025878906 60 M146.79689025878906 -60 C146.79689025878906 -20.098639988552726, 146.79689025878906 19.802720022894547, 146.79689025878906 60 M146.79689025878906 60 C84.55228998065192 60, 22.307689702514793 60, -146.79689025878906 60 M146.79689025878906 60 C56.83346167114762 60, -33.12996691649383 60, -146.79689025878906 60 M-146.79689025878906 60 C-146.79689025878906 34.579049627232436, -146.79689025878906 9.15809925446488, -146.79689025878906 -60 M-146.79689025878906 60 C-146.79689025878906 24.152083036745324, -146.79689025878906 -11.695833926509351, -146.79689025878906 -60"></path></g><g transform="translate(0, -36)" class="annotation-group text"></g><g transform="translate(-127.79375457763672, -36)" class="label-group text"><g transform="translate(0,-12)" style="font-weight: bolder" class="label"><foreignObject height="24" width="255.58750915527344"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 346px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>PropertyChangeHandler&lt;Functor&gt;</p></span></div></foreignObject></g></g><g transform="translate(-134.79689025878906, 12)" class="members-group text"><g transform="translate(0,-12)" style="" class="label"><foreignObject height="24" width="141.8000030517578"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 206px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>-Functor m_handler</p></span></div></foreignObject></g></g><g transform="translate(-134.79689025878906, 60)" class="methods-group text"></g><g style="" class="divider"><path style="" fill="none" stroke-width="1.3" stroke="#ccc" d="M-146.79689025878906 -12 C-32.50942246130626 -12, 81.77804533617655 -12, 146.79689025878906 -12 M-146.79689025878906 -12 C-39.51999909035709 -12, 67.75689207807488 -12, 146.79689025878906 -12"></path></g><g style="" class="divider"><path style="" fill="none" stroke-width="1.3" stroke="#ccc" d="M-146.79689025878906 36 C-56.76780001612556 36, 33.26129022653794 36, 146.79689025878906 36 M-146.79689025878906 36 C-60.53608545978163 36, 25.7247193392258 36, 146.79689025878906 36"></path></g></g><g transform="translate(1438.5531768798828, 334)" id="classId-PropertyNotifier-9" class="node default"><g class="basic label-container"><path style="" fill="#1f2020" stroke-width="0" stroke="none" d="M-132.7093734741211 -60 L132.7093734741211 -60 L132.7093734741211 60 L-132.7093734741211 60"></path><path style="" fill="none" stroke-width="1.3" stroke="#ccc" d="M-132.7093734741211 -60 C-69.29950026416229 -60, -5.889627054203487 -60, 132.7093734741211 -60 M-132.7093734741211 -60 C-65.72686346809648 -60, 1.2556465379281292 -60, 132.7093734741211 -60 M132.7093734741211 -60 C132.7093734741211 -24.705342435893265, 132.7093734741211 10.589315128213471, 132.7093734741211 60 M132.7093734741211 -60 C132.7093734741211 -35.857963405483964, 132.7093734741211 -11.715926810967929, 132.7093734741211 60 M132.7093734741211 60 C60.3217705138496 60, -12.065832446421894 60, -132.7093734741211 60 M132.7093734741211 60 C76.75953281359858 60, 20.80969215307607 60, -132.7093734741211 60 M-132.7093734741211 60 C-132.7093734741211 29.760646043009828, -132.7093734741211 -0.47870791398034385, -132.7093734741211 -60 M-132.7093734741211 60 C-132.7093734741211 28.88378215112764, -132.7093734741211 -2.2324356977447195, -132.7093734741211 -60"></path></g><g transform="translate(0, -36)" class="annotation-group text"></g><g transform="translate(-61.54375076293945, -36)" class="label-group text"><g transform="translate(0,-12)" style="font-weight: bolder" class="label"><foreignObject height="24" width="123.0875015258789"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 172px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>PropertyNotifier</p></span></div></foreignObject></g></g><g transform="translate(-120.7093734741211, 12)" class="members-group text"><g transform="translate(0,-12)" style="" class="label"><foreignObject height="24" width="179.875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 242px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>-std::function m_handler</p></span></div></foreignObject></g></g><g transform="translate(-120.7093734741211, 60)" class="methods-group text"></g><g style="" class="divider"><path style="" fill="none" stroke-width="1.3" stroke="#ccc" d="M-132.70936584472656 -12 C-52.91292776883526 -12, 26.883510307056042 -12, 132.70936584472656 -12 M-132.70936584472656 -12 C-32.20954590433867 -12, 68.29027403604923 -12, 132.70936584472656 -12"></path></g><g style="" class="divider"><path style="" fill="none" stroke-width="1.3" stroke="#ccc" d="M-132.70936584472656 36 C-64.30257817595121 36, 4.10420949282414 36, 132.70936584472656 36 M-132.70936584472656 36 C-28.263188809089144 36, 76.18298822654828 36, 132.70936584472656 36"></path></g></g><g transform="translate(801.8125305175781, 334)" id="classId-PropertyBindingPrivatePtr-10" class="node default"><g class="basic label-container"><path style="" fill="#1f2020" stroke-width="0" stroke="none" d="M-110.4375 -42 L110.4375 -42 L110.4375 42 L-110.4375 42"></path><path style="" fill="none" stroke-width="1.3" stroke="#ccc" d="M-110.4375 -42 C-29.22241191398544 -42, 51.99267617202912 -42, 110.4375 -42 M-110.4375 -42 C-66.22969572388581 -42, -22.02189144777161 -42, 110.4375 -42 M110.4375 -42 C110.4375 -17.266127192950652, 110.4375 7.467745614098696, 110.4375 42 M110.4375 -42 C110.4375 -17.189668870125352, 110.4375 7.620662259749295, 110.4375 42 M110.4375 42 C25.416022884029914 42, -59.60545423194017 42, -110.4375 42 M110.4375 42 C64.03597628097671 42, 17.63445256195341 42, -110.4375 42 M-110.4375 42 C-110.4375 8.847587544713193, -110.4375 -24.304824910573615, -110.4375 -42 M-110.4375 42 C-110.4375 17.59826297350418, -110.4375 -6.803474052991639, -110.4375 -42"></path></g><g transform="translate(0, -18)" class="annotation-group text"></g><g transform="translate(-98.4375, -18)" class="label-group text"><g transform="translate(0,-12)" style="font-weight: bolder" class="label"><foreignObject height="24" width="196.875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 248px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>PropertyBindingPrivatePtr</p></span></div></foreignObject></g></g><g transform="translate(-98.4375, 30)" class="members-group text"></g><g transform="translate(-98.4375, 60)" class="methods-group text"></g><g style="" class="divider"><path style="" fill="none" stroke-width="1.3" stroke="#ccc" d="M-110.4375 6 C-37.133329674290266 6, 36.17084065141947 6, 110.4375 6 M-110.4375 6 C-47.246873197980804 6, 15.943753604038392 6, 110.4375 6"></path></g><g style="" class="divider"><path style="" fill="none" stroke-width="1.3" stroke="#ccc" d="M-110.4375 24 C-46.43188649220666 24, 17.573727015586684 24, 110.4375 24 M-110.4375 24 C-64.9095157263555 24, -19.381531452710973 24, 110.4375 24"></path></g></g><g transform="translate(801.8125305175781, 854)" id="classId-PropertyObserverPointer-11" class="node default"><g class="basic label-container"><path style="" fill="#1f2020" stroke-width="0" stroke="none" d="M-105.875 -42 L105.875 -42 L105.875 42 L-105.875 42"></path><path style="" fill="none" stroke-width="1.3" stroke="#ccc" d="M-105.875 -42 C-58.10323532803227 -42, -10.331470656064539 -42, 105.875 -42 M-105.875 -42 C-50.40352513137274 -42, 5.067949737254523 -42, 105.875 -42 M105.875 -42 C105.875 -22.27040605425779, 105.875 -2.5408121085155813, 105.875 42 M105.875 -42 C105.875 -9.063565115014242, 105.875 23.872869769971516, 105.875 42 M105.875 42 C57.48884207401324 42, 9.102684148026484 42, -105.875 42 M105.875 42 C36.18771881082745 42, -33.499562378345104 42, -105.875 42 M-105.875 42 C-105.875 18.04023557130428, -105.875 -5.919528857391441, -105.875 -42 M-105.875 42 C-105.875 20.362031465465595, -105.875 -1.27593706906881, -105.875 -42"></path></g><g transform="translate(0, -18)" class="annotation-group text"></g><g transform="translate(-93.875, -18)" class="label-group text"><g transform="translate(0,-12)" style="font-weight: bolder" class="label"><foreignObject height="24" width="187.75"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 238px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>PropertyObserverPointer</p></span></div></foreignObject></g></g><g transform="translate(-93.875, 30)" class="members-group text"></g><g transform="translate(-93.875, 60)" class="methods-group text"></g><g style="" class="divider"><path style="" fill="none" stroke-width="1.3" stroke="#ccc" d="M-105.875 6 C-42.18772048720885 6, 21.4995590255823 6, 105.875 6 M-105.875 6 C-40.706174960818686 6, 24.46265007836263 6, 105.875 6"></path></g><g style="" class="divider"><path style="" fill="none" stroke-width="1.3" stroke="#ccc" d="M-105.875 24 C-43.89843521005893 24, 18.078129579882145 24, 105.875 24 M-105.875 24 C-34.35084905655167 24, 37.17330188689667 24, 105.875 24"></path></g></g></g></g></g></svg>