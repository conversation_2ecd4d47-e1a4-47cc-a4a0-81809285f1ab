/**
 * @file Core/PropertyBindingError.cpp
 * @brief PropertyBindingError实现
 */

#include "PropertyBindingError.h"
#include <sstream>

namespace PropertySystem::Core {

// ==================================================================================
// PropertyBindingError实现
// ==================================================================================

PropertyBindingError::PropertyBindingError() = default;

PropertyBindingError::PropertyBindingError(Type type, const std::string& description) {
    if (type != NoError) {
        d_ = std::make_shared<ErrorData>(type, description);
    }
}

PropertyBindingError::PropertyBindingError(const PropertyBindingError &other) = default;
PropertyBindingError::PropertyBindingError(PropertyBindingError &&other) noexcept = default;
PropertyBindingError &PropertyBindingError::operator=(const PropertyBindingError &other) = default;
PropertyBindingError &PropertyBindingError::operator=(PropertyBindingError &&other) noexcept = default;
PropertyBindingError::~PropertyBindingError() = default;

std::string PropertyBindingError::toString() const {
    if (!hasError()) {
        return "No error";
    }
    
    std::ostringstream oss;
    oss << typeToString(type()) << ": " << description();
    return oss.str();
}

void PropertyBindingError::ensureErrorData() {
    if (!d_) {
        d_ = std::make_shared<ErrorData>();
    }
}

} // namespace PropertySystem::Core
