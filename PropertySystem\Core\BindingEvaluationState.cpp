/**
 * @file Core/BindingEvaluationState.cpp
 * @brief 绑定评估状态管理实现
 * 
 * 这个文件实现了绑定评估状态的管理功能，
 * 包括依赖追踪、循环检测和评估上下文管理。
 */

#include "BindingEvaluationState.h"
#include "PropertyBindingPrivate.h"
#include "PropertyBindingData.h"
#include <algorithm>

namespace PropertySystem::Core::Internal {

// ==================================================================================
// 全局状态管理
// ==================================================================================

namespace {
    thread_local BindingEvaluationState* g_current_evaluation_state = nullptr;
}

// ==================================================================================
// BindingStatus实现
// ==================================================================================

/**
 * @brief 绑定状态结构
 * 
 * 管理当前线程的绑定评估状态
 */
struct BindingStatus {
    BindingEvaluationState *currentlyEvaluatingBinding = nullptr;  ///< 当前正在评估的绑定
    std::thread::id threadId;                                      ///< 线程ID
    
    BindingStatus() : threadId(std::this_thread::get_id()) {}
};

// ==================================================================================
// BindingEvaluationState实现
// ==================================================================================

BindingEvaluationState::BindingEvaluationState(PropertyBindingPrivate *binding, BindingStatus *status) 
    : binding(binding) {
    
    if (!status) {
        // 创建默认状态
        static thread_local BindingStatus default_status;
        status = &default_status;
    }
    
    // 保存之前的状态
    previous_state_ = status->currentlyEvaluatingBinding;
    current_state_ = &status->currentlyEvaluatingBinding;
    
    // 设置当前状态
    *current_state_ = this;
    g_current_evaluation_state = this;
}

bool BindingEvaluationState::addCapturedProperty(const PropertyBindingData *property) {
    if (!property) return false;
    
    // 检查是否已存在
    auto it = std::find(already_captured_properties_.begin(), 
                       already_captured_properties_.end(), 
                       property);
    
    if (it != already_captured_properties_.end()) {
        return false; // 已存在
    }
    
    // 添加到列表
    already_captured_properties_.push_back(property);
    return true;
}

bool BindingEvaluationState::isPropertyCaptured(const PropertyBindingData *property) const {
    if (!property) return false;
    
    auto it = std::find(already_captured_properties_.begin(), 
                       already_captured_properties_.end(), 
                       property);
    
    return it != already_captured_properties_.end();
}

void BindingEvaluationState::clearCapturedProperties() {
    already_captured_properties_.clear();
}

size_t BindingEvaluationState::capturedPropertyCount() const {
    return already_captured_properties_.size();
}

const PropertyBindingData* BindingEvaluationState::getCapturedProperty(size_t index) const {
    if (index < already_captured_properties_.size()) {
        return already_captured_properties_[index];
    }
    return nullptr;
}

bool BindingEvaluationState::hasCircularDependency(const PropertyBindingData *property) const {
    // 简化的循环依赖检测
    // 检查当前属性是否已经在评估栈中
    BindingEvaluationState* current = previous_state_;
    while (current) {
        if (current->isPropertyCaptured(property)) {
            return true; // 发现循环依赖
        }
        current = current->previous_state_;
    }
    return false;
}

void BindingEvaluationState::markDependency(const PropertyBindingData *property) {
    if (property && !isPropertyCaptured(property)) {
        if (!hasCircularDependency(property)) {
            addCapturedProperty(property);
        }
    }
}

BindingEvaluationState* BindingEvaluationState::current() {
    return g_current_evaluation_state;
}

PropertyBindingPrivate* BindingEvaluationState::currentBinding() {
    BindingEvaluationState* state = current();
    return state ? state->binding : nullptr;
}

bool BindingEvaluationState::isEvaluating() {
    return current() != nullptr;
}

void BindingEvaluationState::suspend() {
    if (current_state_) {
        *current_state_ = previous_state_;
        g_current_evaluation_state = previous_state_;
    }
}

void BindingEvaluationState::resume() {
    if (current_state_) {
        *current_state_ = this;
        g_current_evaluation_state = this;
    }
}

// ==================================================================================
// 兼容性安全点实现
// ==================================================================================

CompatPropertySafePoint::CompatPropertySafePoint(BindingStatus *status, UntypedPropertyData *property)
    : property_(property) {
    
    if (status) {
        // 保存当前状态
        previous_state_ = status->currentCompatProperty;
        current_state_ = &status->currentCompatProperty;
        currently_evaluating_binding_list_ = &status->currentlyEvaluatingBinding;
        binding_state_ = status->currentlyEvaluatingBinding;
        
        // 设置新状态
        status->currentCompatProperty = this;
    }
}

UntypedPropertyData* CompatPropertySafePoint::property() const {
    return property_;
}

CompatPropertySafePoint* CompatPropertySafePoint::current() const {
    return current_state_ ? *current_state_ : nullptr;
}

// ==================================================================================
// 全局辅助函数
// ==================================================================================

bool isAnyBindingEvaluating() {
    return BindingEvaluationState::isEvaluating();
}

BindingEvaluationState* suspendCurrentBindingStatus() {
    BindingEvaluationState* current = BindingEvaluationState::current();
    if (current) {
        current->suspend();
    }
    return current;
}

void restoreBindingStatus(BindingEvaluationState *status) {
    if (status) {
        status->resume();
    }
}

} // namespace PropertySystem::Core::Internal

// ==================================================================================
// 全局命名空间别名
// ==================================================================================

using PropertySystem::Core::Internal::BindingEvaluationState;
using PropertySystem::Core::Internal::BindingStatus;
using PropertySystem::Core::Internal::CompatPropertySafePoint;
