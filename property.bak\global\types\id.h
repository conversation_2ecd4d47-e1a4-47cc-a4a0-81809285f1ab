﻿#ifndef ID_H
#define ID_H

#include <array>
#include <string>
#include <cstring>
#include <sstream>
#include <iomanip>
#include <functional>

#include "utils/uuid.h"

class Id
{
    friend std::ostream& operator<<(std::ostream& os, const Id& id) {
        os << id.toString();
        return os;
    }
public:
    Id() : data(Uuid::createUuid()) {}
    ~Id() = default;

    explicit Id(const std::string& str) {
        fromString(str);
    }

    explicit Id(const uint8_t* bytes) {
        std::memcpy(data.data(), bytes, Uuid::UUID_SIZE);
    }

    explicit Id(const Uuid::UuidArray& bytes) {
        std::memcpy(data.data(), bytes.data(), 16);
    }

    Id(const Id& other) = default;
    Id(Id&& other) noexcept = default;
    Id& operator=(const Id& other) = default;
    Id& operator=(Id&& other) noexcept = default;

    bool operator==(const Id& other) const { return data == other.data; }
    bool operator!=(const Id& other) const { return !(*this == other); }
    bool operator<(const Id& other) const  { return data < other.data; }
    bool operator<=(const Id& other) const { return data <= other.data; }

    std::string toString() const {
        std::stringstream ss;
        ss << std::hex << std::setfill('0');

        for (size_t i = 0; i < Uuid::UUID_SIZE; ++i) {
            ss << std::setw(2) << static_cast<int>(data[i]);
            if (i == 3 || i == 5 || i == 7 || i == 9) {
                ss << '-';
            }
        }
        return ss.str();
    }

    const uint8_t* bytes() const { return data.data(); }

    explicit operator bool() const {
        static Id nullId { false };
        return *this != nullId;
    }

private:
    void fromString(const std::string& str) {
        std::string cleanStr;
        cleanStr.reserve(32);

        for (char c : str) {
            if (c != '-' && !std::isspace(c)) {
                cleanStr.push_back(c);
            }
        }

        if (cleanStr.length() != 32) {
            throw std::invalid_argument("Invalid UUID string format");
        }

        for (size_t i = 0; i < Uuid::UUID_SIZE; ++i) {
            std::string byteStr = cleanStr.substr(i * 2, 2);
            data[i] = static_cast<uint8_t>(std::stoul(byteStr, nullptr, 16));
        }
    }

    Uuid::UuidArray data { };

    // Default construction is allowed but marked as private
    // for creating invalid instances
    Id(bool) {}
};

// Hash support
namespace std {
template<>
struct hash<Id> {
    size_t operator()(const Id& id) const {
        const auto& data = *reinterpret_cast<const array<uint8_t, Uuid::UUID_SIZE>*>(id.bytes());
        return hash<string_view>()(
            string_view(reinterpret_cast<const char*>(data.data()), data.size()));
    }
};
}

#endif // ID_H
