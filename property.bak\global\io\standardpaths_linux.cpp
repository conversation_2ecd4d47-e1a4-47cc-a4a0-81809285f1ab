﻿#ifdef __linux__

#include "standardpaths.h"

#include <unistd.h>
#include <pwd.h>
#include <fstream>
#include <sstream>
#include <stdexcept>
#include <filesystem>
#include <cstdlib>
#include <algorithm>
#include <system_error>

namespace {

// Helper function to get XDG directory from config or default
std::string getXdgDirectory(const char* xdgVar, const char* defaultSubdir) {
    const char* home = getenv("HOME");
    if (!home) home = getpwuid(getuid())->pw_dir;

    // Try to read from XDG config
    std::string configPath = std::string(home) + "/.config/user-dirs.dirs";
    std::ifstream file(configPath);
    std::string line;

    while (std::getline(file, line)) {
        size_t pos = line.find(xdgVar);
        if (pos != std::string::npos) {
            size_t start = line.find('"') + 1;
            size_t end = line.find_last_of('"');
            if (start != std::string::npos && end != std::string::npos && start < end) {
                std::string path = line.substr(start, end - start);

                // Replace environment variables
                size_t homePos = path.find("$HOME");
                if (homePos != std::string::npos) {
                    path.replace(homePos, 5, home);
                }

                return path;
            }
        }
    }

    // Use default path
    return std::string(home) + "/" + defaultSubdir;
}

} // anonymous namespace

std::string StandardPaths::getPath(DirectoryType type) {
    const char* xdgVar = nullptr;
    const char* defaultSubdir = nullptr;

    switch (type) {
    case DirectoryType::Home: {
        const char* home = getenv("HOME");
        if (!home) home = getpwuid(getuid())->pw_dir;
        return formatPath(home);
    }
    case DirectoryType::Documents:
        return formatPath(getXdgDirectory("XDG_DOCUMENTS_DIR", "Documents"));
    case DirectoryType::Downloads:
        return formatPath(getXdgDirectory("XDG_DOWNLOAD_DIR", "Downloads"));
    case DirectoryType::Music:
        return formatPath(getXdgDirectory("XDG_MUSIC_DIR", "Music"));
    case DirectoryType::Pictures:
        return formatPath(getXdgDirectory("XDG_PICTURES_DIR", "Pictures"));
    case DirectoryType::Videos:
        return formatPath(getXdgDirectory("XDG_PICTURES_DIR", "Videos"));
    case DirectoryType::Desktop:
        return formatPath(getXdgDirectory("XDG_DESKTOP_DIR", "Desktop"));
    case DirectoryType::AppData: {
        const char* xdgDataHome = getenv("XDG_DATA_HOME");
        std::string path;
        if (xdgDataHome && *xdgDataHome) {
            path = xdgDataHome;
        } else {
            const char* home = getenv("HOME");
            if (!home) home = getpwuid(getuid())->pw_dir;
            path = std::string(home) + "/.local/share";
        }
        return formatPath(path);
    }
    case DirectoryType::Temp:
        return formatPath(std::filesystem::temp_directory_path().string());
    case DirectoryType::Current:
        return formatPath(std::filesystem::current_path().string());
    case DirectoryType::Config:
        return formatPath(std::string(getenv("HOME")) + "/.config");
    case DirectoryType::Cache:
        return formatPath(std::string(getenv("HOME")) + "/.cache");
    default:
        throw std::invalid_argument("Unsupported directory type");
    }
}

std::vector<std::string> StandardPaths::standardLocations(DirectoryType type) {
    std::vector<std::string> paths;
    paths.push_back(getPath(type));

    // Add alternative locations if available
    switch (type) {
    case DirectoryType::AppData: {
        // Add system-wide data directories
        const char* xdgDataDirs = getenv("XDG_DATA_DIRS");
        if (xdgDataDirs && *xdgDataDirs) {
            std::string dataDirs(xdgDataDirs);
            std::stringstream ss(dataDirs);
            std::string dir;

            while (std::getline(ss, dir, ':')) {
                if (!dir.empty()) {
                    paths.push_back(formatPath(dir));
                }
            }
        } else {
            // Default XDG data dirs if not set
            paths.push_back(formatPath("/usr/local/share"));
            paths.push_back(formatPath("/usr/share"));
        }
        break;
    }
    case DirectoryType::Config: {
        // Add system-wide config directories
        const char* xdgConfigDirs = getenv("XDG_CONFIG_DIRS");
        if (xdgConfigDirs && *xdgConfigDirs) {
            std::string configDirs(xdgConfigDirs);
            std::stringstream ss(configDirs);
            std::string dir;

            while (std::getline(ss, dir, ':')) {
                if (!dir.empty()) {
                    paths.push_back(formatPath(dir));
                }
            }
        } else {
            // Default XDG config dir if not set
            paths.push_back(formatPath("/etc/xdg"));
        }
        break;
    }
    default:
        break;
    }

    return paths;
}

// Helper method to format paths according to the requested format
std::string StandardPaths::formatPath(const std::string& path) {
    if (path.empty()) {
        return path;
    }

    std::string result = path;

    if (!result.empty() && result.back() != '/') {
        result += '/';
    }

    return result;
}

#endif
