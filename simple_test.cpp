/**
 * @file simple_test.cpp
 * @brief 简化的Property系统测试
 * 
 * 这个文件用于验证Property系统的基础结构是否正确。
 */

#include "PropertySystem/PropertySystem.h"
#include <iostream>

int main() {
    std::cout << "=== Property System Structure Test ===" << std::endl;
    
    try {
        // 测试基础PropertyData
        PropertySystem::Core::PropertyData<int> data(42);
        std::cout << "PropertyData<int> created with value: " << data.valueBypassingBindings() << std::endl;
        
        // 测试错误类
        PropertySystem::Core::PropertyBindingError error;
        std::cout << "PropertyBindingError created, hasError: " << error.hasError() << std::endl;
        
        // 测试绑定位置
        PropertySystem::PropertyBindingSourceLocation location = PROPERTY_DEFAULT_BINDING_LOCATION;
        std::cout << "PropertyBindingSourceLocation created" << std::endl;
        
        // 测试更新组
        std::cout << "Update group depth: " << PropertySystem::Core::getPropertyUpdateGroupDepth() << std::endl;
        
        // 测试系统信息
        std::cout << "Property System Version: " << PropertySystem::getPropertySystemVersion() << std::endl;
        std::cout << "Build Info: " << PropertySystem::getPropertySystemBuildInfo() << std::endl;
        
        std::cout << "✓ All basic structures work correctly!" << std::endl;
        return 0;
        
    } catch (const std::exception& e) {
        std::cerr << "Error: " << e.what() << std::endl;
        return 1;
    } catch (...) {
        std::cerr << "Unknown error occurred" << std::endl;
        return 1;
    }
}
