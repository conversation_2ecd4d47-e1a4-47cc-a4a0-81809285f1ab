/**
 * @file PropertySystem/Binding/PropertyBinding.h
 * @brief 现代化的属性绑定实现，移除MetaType依赖
 * 
 * 这个文件提供了简化的属性绑定实现，移除了不必要的MetaType参数，
 * 提高了性能并简化了接口。
 */

#ifndef PROPERTY_SYSTEM_BINDING_PROPERTY_BINDING_H
#define PROPERTY_SYSTEM_BINDING_PROPERTY_BINDING_H

#include <memory>
#include <utility>
#include <source_location>
#include "BindingVTable.h"
#include "BindingLocation.h"
#include "../Core/Concepts.h"

// ==================================================================================
// 前向声明
// ==================================================================================

class PropertyBindingPrivate;
class PropertyBindingError;
class UntypedPropertyData;

namespace PropertySystem::Binding {

// ==================================================================================
// 现代化的未类型化属性绑定
// ==================================================================================

/**
 * @brief 现代化的未类型化属性绑定类
 * 
 * 这个类实现了属性之间的绑定关系，移除了MetaType依赖，
 * 提供更简洁和高效的接口。
 */
class ModernUntypedPropertyBinding {
public:
    /**
     * @brief 默认构造函数，创建空绑定
     */
    ModernUntypedPropertyBinding();
    
    /**
     * @brief 从绑定函数创建绑定
     * @param bindingFunction 绑定函数包装器
     * @param location 源代码位置信息
     */
    ModernUntypedPropertyBinding(const ModernPropertyBindingFunction &bindingFunction,
                                const PropertyBindingSourceLocation &location = PROPERTY_DEFAULT_BINDING_LOCATION);
    
    /**
     * @brief 拷贝构造函数
     * @param other 其他绑定对象
     */
    ModernUntypedPropertyBinding(const ModernUntypedPropertyBinding &other);
    
    /**
     * @brief 移动构造函数
     * @param other 其他绑定对象
     */
    ModernUntypedPropertyBinding(ModernUntypedPropertyBinding &&other) noexcept;
    
    /**
     * @brief 析构函数
     */
    ~ModernUntypedPropertyBinding();
    
    /**
     * @brief 拷贝赋值操作符
     * @param other 其他绑定对象
     * @return 当前对象的引用
     */
    ModernUntypedPropertyBinding &operator=(const ModernUntypedPropertyBinding &other);
    
    /**
     * @brief 移动赋值操作符
     * @param other 其他绑定对象
     * @return 当前对象的引用
     */
    ModernUntypedPropertyBinding &operator=(ModernUntypedPropertyBinding &&other) noexcept;

    // ==================================================================================
    // 状态查询
    // ==================================================================================
    
    /**
     * @brief 检查绑定是否为空
     * @return 如果绑定为空则返回true
     */
    bool isNull() const;
    
    /**
     * @brief 检查绑定是否有效
     * @return 如果绑定有效则返回true
     */
    bool isValid() const;
    
    /**
     * @brief 获取绑定错误信息
     * @return 错误信息对象
     */
    PropertyBindingError error() const;
    
    /**
     * @brief 获取源代码位置信息
     * @return 源代码位置信息
     */
    PropertyBindingSourceLocation sourceLocation() const;

    // ==================================================================================
    // 内部接口
    // ==================================================================================
    
protected:
    /**
     * @brief 从私有数据构造（内部使用）
     * @param priv 私有数据指针
     */
    explicit ModernUntypedPropertyBinding(PropertyBindingPrivate *priv);

private:
    friend class PropertyBindingData;
    friend class PropertyBindingPrivate;
    template <typename> friend class ModernPropertyBinding;
    
    std::shared_ptr<PropertyBindingPrivate> d;
};

// ==================================================================================
// 现代化的类型化属性绑定
// ==================================================================================

/**
 * @brief 现代化的类型化属性绑定模板类
 * @tparam PropertyType 属性类型
 * 
 * 这个类提供类型安全的属性绑定，确保绑定函数的返回类型
 * 与属性类型匹配。
 */
template <typename PropertyType>
requires PropertySystem::Core::PropertyType<PropertyType>
class ModernPropertyBinding : public ModernUntypedPropertyBinding {
public:
    using value_type = PropertyType;
    
    /**
     * @brief 默认构造函数
     */
    ModernPropertyBinding() = default;
    
    /**
     * @brief 从可调用对象创建绑定
     * @tparam Functor 可调用对象类型
     * @param f 可调用对象
     * @param location 源代码位置信息
     */
    template <typename Functor>
    requires PropertySystem::Core::PropertyBindingCallable<Functor, PropertyType>
    ModernPropertyBinding(Functor &&f, const PropertyBindingSourceLocation &location = PROPERTY_DEFAULT_BINDING_LOCATION)
        : ModernUntypedPropertyBinding(
            makeTypedBinding<PropertyType>(std::forward<Functor>(f)), 
            location) {}
    
    /**
     * @brief 从未类型化绑定创建（如果类型匹配）
     * @param other 未类型化绑定
     */
    ModernPropertyBinding(const ModernUntypedPropertyBinding &other)
        : ModernUntypedPropertyBinding(other) {}
    
    /**
     * @brief 从未类型化绑定移动创建（如果类型匹配）
     * @param other 未类型化绑定
     */
    ModernPropertyBinding(ModernUntypedPropertyBinding &&other)
        : ModernUntypedPropertyBinding(std::move(other)) {}
};

// ==================================================================================
// 便利函数
// ==================================================================================

/**
 * @brief 创建现代化属性绑定的便利函数
 * @tparam Functor 可调用对象类型
 * @param f 绑定函数
 * @param location 源代码位置信息
 * @return 类型化的属性绑定对象
 */
template <typename Functor>
requires std::invocable<Functor> && PropertySystem::Core::PropertyType<std::invoke_result_t<Functor>>
auto makeModernPropertyBinding(Functor &&f, const PropertyBindingSourceLocation &location = PROPERTY_DEFAULT_BINDING_LOCATION) {
    return ModernPropertyBinding<std::invoke_result_t<Functor>>(std::forward<Functor>(f), location);
}

} // namespace PropertySystem::Binding

// ==================================================================================
// 全局命名空间别名
// ==================================================================================

// 导出现代化版本到全局命名空间
using PropertySystem::Binding::ModernUntypedPropertyBinding;
using PropertySystem::Binding::ModernPropertyBinding;
using PropertySystem::Binding::makeModernPropertyBinding;

#endif // PROPERTY_SYSTEM_BINDING_PROPERTY_BINDING_H
