/**
 * @file test_property_system.cpp
 * @brief Property系统测试文件
 * 
 * 这个文件用于测试Property系统的核心功能，
 * 验证属性绑定、观察者模式和更新组的正确性。
 */

#include "PropertySystem/PropertySystem.h"
#include <iostream>
#include <string>
#include <cassert>

using namespace PropertySystem;

// ==================================================================================
// 基础功能测试
// ==================================================================================

void testBasicProperty() {
    std::cout << "Testing basic property functionality..." << std::endl;
    
    // 测试基本的PropertyData功能
    PropertyData<int> data(42);
    assert(data.valueBypassingBindings() == 42);
    
    data.setValueBypassingBindings(100);
    assert(data.valueBypassingBindings() == 100);
    
    // 测试类型转换
    int value = data;
    assert(value == 100);
    
    std::cout << "✓ Basic property functionality works" << std::endl;
}

void testPropertyTypes() {
    std::cout << "Testing property type concepts..." << std::endl;
    
    // 测试不同类型的属性
    PropertyData<int> intProp(42);
    PropertyData<double> doubleProp(3.14);
    PropertyData<std::string> stringProp("Hello");
    
    assert(intProp.valueBypassingBindings() == 42);
    assert(doubleProp.valueBypassingBindings() == 3.14);
    assert(stringProp.valueBypassingBindings() == "Hello");
    
    // 测试比较操作
    PropertyData<int> intProp2(42);
    assert(intProp == intProp2);
    
    intProp2.setValueBypassingBindings(43);
    assert(intProp != intProp2);
    
    std::cout << "✓ Property type concepts work" << std::endl;
}

void testPropertyBinding() {
    std::cout << "Testing property binding..." << std::endl;
    
    // 测试绑定错误类
    PropertyBindingError noError;
    assert(!noError.hasError());
    assert(noError.type() == PropertyBindingError::NoError);
    
    PropertyBindingError bindingLoop = PropertyBindingError::createBindingLoopError("Test loop");
    assert(bindingLoop.hasError());
    assert(bindingLoop.type() == PropertyBindingError::BindingLoop);
    assert(bindingLoop.description() == "Test loop");
    
    // 测试错误类型转换
    std::string typeStr = PropertyBindingError::typeToString(PropertyBindingError::BindingLoop);
    assert(typeStr == "BindingLoop");
    
    PropertyBindingError::Type parsedType = PropertyBindingError::typeFromString("EvaluationError");
    assert(parsedType == PropertyBindingError::EvaluationError);
    
    std::cout << "✓ Property binding error handling works" << std::endl;
}

void testBindingLocation() {
    std::cout << "Testing binding location..." << std::endl;
    
    // 测试默认绑定位置
    PropertyBindingSourceLocation defaultLoc = PROPERTY_DEFAULT_BINDING_LOCATION;
    
    // 在支持source_location的编译器上，这些值应该被设置
    #ifdef PROPERTY_COLLECT_BINDING_LOCATION
    assert(defaultLoc.fileName != nullptr);
    assert(defaultLoc.functionName != nullptr);
    assert(defaultLoc.line > 0);
    #endif
    
    std::cout << "✓ Binding location works" << std::endl;
}

void testUpdateGroup() {
    std::cout << "Testing update group..." << std::endl;
    
    // 测试更新组状态
    assert(!isInPropertyUpdateGroup());
    assert(getPropertyUpdateGroupDepth() == 0);
    
    // 测试手动更新组
    beginPropertyUpdateGroup();
    assert(isInPropertyUpdateGroup());
    assert(getPropertyUpdateGroupDepth() == 1);
    
    beginPropertyUpdateGroup();
    assert(getPropertyUpdateGroupDepth() == 2);
    
    endPropertyUpdateGroup();
    assert(getPropertyUpdateGroupDepth() == 1);
    
    endPropertyUpdateGroup();
    assert(!isInPropertyUpdateGroup());
    assert(getPropertyUpdateGroupDepth() == 0);
    
    // 测试RAII更新组
    {
        ScopedPropertyUpdateGroup group;
        assert(isInPropertyUpdateGroup());
        assert(getPropertyUpdateGroupDepth() == 1);
    }
    assert(!isInPropertyUpdateGroup());
    
    // 测试便利函数
    bool executed = false;
    withUpdateGroup([&]() {
        assert(isInPropertyUpdateGroup());
        executed = true;
    });
    assert(executed);
    assert(!isInPropertyUpdateGroup());
    
    std::cout << "✓ Update group functionality works" << std::endl;
}

void testBindingEvaluationState() {
    std::cout << "Testing binding evaluation state..." << std::endl;
    
    // 测试绑定状态管理
    assert(!isAnyBindingEvaluating());
    
    auto* status = Internal::getBindingStatus();
    assert(status != nullptr);
    assert(status->currently_evaluating_binding == nullptr);
    
    // 测试线程ID检查
    assert(status->isInCorrectThread());
    
    std::cout << "✓ Binding evaluation state works" << std::endl;
}

void testUntypedPropertyBinding() {
    std::cout << "Testing untyped property binding..." << std::endl;
    
    // 测试空绑定
    UntypedPropertyBinding emptyBinding;
    assert(emptyBinding.isNull());
    assert(!emptyBinding.isValid());
    
    // 测试绑定比较
    UntypedPropertyBinding emptyBinding2;
    assert(emptyBinding == emptyBinding2);
    
    // 测试绑定交换
    UntypedPropertyBinding binding1, binding2;
    binding1.swap(binding2);
    assert(binding1 == binding2);
    
    std::cout << "✓ Untyped property binding works" << std::endl;
}

// ==================================================================================
// 集成测试
// ==================================================================================

void testSystemIntegration() {
    std::cout << "Testing system integration..." << std::endl;
    
    // 测试系统版本信息
    const char* version = getPropertySystemVersion();
    const char* buildInfo = getPropertySystemBuildInfo();
    
    assert(version != nullptr);
    assert(buildInfo != nullptr);
    
    std::cout << "Property System Version: " << version << std::endl;
    std::cout << "Build Info: " << buildInfo << std::endl;
    
    // 测试系统配置
    auto& config = PropertySystemConfig::global();
    assert(!config.enableDebugMode); // 默认应该是false
    assert(config.enableAutomaticCleanup); // 默认应该是true
    assert(config.maxObserversPerProperty == 1000); // 默认值
    
    std::cout << "✓ System integration works" << std::endl;
}

// ==================================================================================
// 主测试函数
// ==================================================================================

int main() {
    std::cout << "=== Property System Test Suite ===" << std::endl;
    std::cout << std::endl;
    
    try {
        // 基础功能测试
        testBasicProperty();
        testPropertyTypes();
        testPropertyBinding();
        testBindingLocation();
        testUpdateGroup();
        testBindingEvaluationState();
        testUntypedPropertyBinding();
        
        // 集成测试
        testSystemIntegration();
        
        std::cout << std::endl;
        std::cout << "=== All Tests Passed! ===" << std::endl;
        return 0;
        
    } catch (const std::exception& e) {
        std::cerr << "Test failed with exception: " << e.what() << std::endl;
        return 1;
    } catch (...) {
        std::cerr << "Test failed with unknown exception" << std::endl;
        return 1;
    }
}
