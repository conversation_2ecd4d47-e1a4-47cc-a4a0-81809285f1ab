﻿#include "fileinfo.h"

#include <filesystem>
#include <iostream>

namespace filesystem {

FileInfo::FileInfo(const Path& path)
    : m_entry(path.toWString())
{}

FileInfo::FileInfo(const std::string& str)
    : m_entry(Path(str).toWString())
{}

FileInfo::FileInfo(const std::filesystem::directory_entry& entry)
    : m_entry(entry)
{}

FileInfo::FileInfo(std::filesystem::directory_entry&& entry)
    : m_entry(std::move(entry))
{}

FileInfo::FileInfo(const FileInfo& other) = default;

FileInfo::FileInfo(FileInfo&& other) noexcept = default;

FileInfo& FileInfo::operator=(const FileInfo& other) = default;

FileInfo& FileInfo::operator=(FileInfo&& other) noexcept = default;

std::string FileInfo::fileName() const
{
    try {
        return m_entry.path().filename().string();
    } catch (...) {
        std::cerr << "FileInfo::filePath: Failed to obtain the file name."
                  << std::endl;
    }
    return "";
}

std::string FileInfo::baseName() const
{
    std::string name = fileName();
    if (name.empty()) {
        return "";
    }
    size_t lastDot = name.find_last_of('.');
    if (lastDot == std::string::npos) {
        return std::string(name);
    }
    return std::string(name.substr(0, lastDot));
}

std::string FileInfo::extension() const
{
    std::string name = fileName();
    if (name.empty()) {
        return "";
    }
    size_t lastDot = name.find_last_of('.');
    if (lastDot == std::string::npos || lastDot == name.size() - 1) {
        return "";
    }
    return std::string(name.substr(lastDot + 1));
}

std::string FileInfo::filePath() const
{
    try {
        return m_entry.path().string();
    } catch (...) {
        std::cerr << "FileInfo::filePath: Failed to obtain the file path."
                  << std::endl;
    }
    return "";
}

std::string FileInfo::absolutePath() const
{
    return fs::absolute(m_entry.path()).parent_path().string();
}

std::string FileInfo::absoluteFilePath() const
{
    return fs::absolute(m_entry.path()).string();
}

bool FileInfo::exists() const
{
    try {
        // Check if we have a valid entry
        return m_entry.exists();
    } catch (...) {
        return false;
    }
}

bool FileInfo::isFile() const
{
    if (exists()) {
        return m_entry.is_regular_file();
    }
    return false;
}

bool FileInfo::isDir() const
{
    if (exists()) {
        return m_entry.is_directory();
    }
    return false;
}

bool FileInfo::isSymLink() const
{
    if (exists()) {
        return m_entry.is_symlink();
    }
    return false;
}

bool FileInfo::isHidden() const
{
#ifdef _WIN32
    return isWindowsHiddenFile();
#else
    // On Unix-like systems, hidden files start with a dot
    auto name = fileName();
    return !name.empty() && name[0] == '.';
#endif
}

std::uintmax_t FileInfo::size() const
{
    if (exists() && isFile()) {
        return m_entry.file_size();
    }
    return 0;
}

std::chrono::system_clock::time_point FileInfo::lastModified() const
{
    if (exists()) {
        auto fileTime = m_entry.last_write_time();

        // Convert file_time_type to system_clock::time_point
        // This is a simplified conversion that may not be accurate for all platforms
        auto now = std::chrono::system_clock::now();
        auto fileNow = fs::file_time_type::clock::now();
        auto diff = fileTime - fileNow;
        return now + std::chrono::duration_cast<std::chrono::system_clock::duration>(diff);
    }
    return std::chrono::system_clock::time_point{};
}

std::chrono::system_clock::time_point FileInfo::lastAccessed() const
{
    try {
#if defined(_WIN32) || defined(__APPLE__) || (defined(__linux__) && __GLIBC__ >= 2 && __GLIBC_MINOR__ >= 28)
        // Try to get more accurate access time using stat
        struct stat st;
        if (stat(m_entry.path().string().c_str(), &st) == 0) {
            // Get the difference between access time and modification time
            auto atime_sec = static_cast<long long>(st.st_atime);
            auto mtime_sec = static_cast<long long>(st.st_mtime);
            auto time_diff = std::chrono::seconds(atime_sec - mtime_sec);

            // Apply this difference to the last_write_time
            auto base_time = fs::last_write_time(m_entry.path());

            // Convert to time_point with appropriate duration
            using fs_duration = typename fs::file_time_type::duration;
            auto adjusted_time = base_time + std::chrono::duration_cast<fs_duration>(time_diff);

            // Convert to system_clock time point
            auto toSystemTime = [](const fs::file_time_type& ft) -> std::chrono::system_clock::time_point {
                auto systemTime = std::chrono::time_point_cast<std::chrono::system_clock::duration>(
                    ft - fs::file_time_type::clock::now() + std::chrono::system_clock::now());
                return systemTime;
            };

            return toSystemTime(adjusted_time);
        }
#endif
    } catch (...) {
        // Ignore errors and use fallback
    }

    return lastModified();
}

std::chrono::system_clock::time_point FileInfo::created() const
{
#ifdef _WIN32
    try {
        // Get Windows creation time
        WIN32_FILE_ATTRIBUTE_DATA fileInfo;
        std::string path = filePath();
        if (GetFileAttributesExA(path.c_str(), GetFileExInfoStandard, &fileInfo)) {
            FILETIME creationTime = fileInfo.ftCreationTime;

            // Convert FILETIME to ULARGE_INTEGER for easier manipulation
            ULARGE_INTEGER uli;
            uli.LowPart = creationTime.dwLowDateTime;
            uli.HighPart = creationTime.dwHighDateTime;

            // Convert Windows FILETIME (100-nanosecond intervals since January 1, 1601)
            // to Unix epoch time (seconds since January 1, 1970)
            constexpr uint64_t WINDOWS_TICK_PER_SECOND = 10'000'000;  // 100ns
            constexpr uint64_t SEC_TO_UNIX_EPOCH = 11'644'473'600; // 1601-1970

            time_t time = static_cast<time_t>((uli.QuadPart / WINDOWS_TICK_PER_SECOND) - SEC_TO_UNIX_EPOCH);

            // Convert to system_clock::time_point
            return std::chrono::system_clock::from_time_t(time);
        }
    } catch (...) {
        // Ignore exceptions
    }
#endif

    // Fallback to last modified time
    return lastModified();
}

void FileInfo::refresh()
{
    try {
        m_entry.refresh();
    } catch (...) {
        // Ignore exceptions - entry might be empty or invalid
    }
}

void FileInfo::setFile(const Path& path) {
    setFile(path.toString());
}

void FileInfo::setFile(const std::string& path)
{
    m_entry = fs::directory_entry(path);
}

#ifdef _WIN32
DWORD FileInfo::getWindowsFileAttributes() const {
    std::string path = filePath();
    DWORD attributes = GetFileAttributesA(path.c_str());
    if (attributes == INVALID_FILE_ATTRIBUTES) {
        return 0;
    }
    return attributes;
}

bool FileInfo::isWindowsHiddenFile() const {
    DWORD attributes = getWindowsFileAttributes();
    return (attributes != 0) && ((attributes & FILE_ATTRIBUTE_HIDDEN) != 0);
}
#endif

} // namespace filesystem
