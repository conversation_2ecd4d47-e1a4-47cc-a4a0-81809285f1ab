#ifndef SHAREDDATA_H
#define SHAREDDATA_H

// #include <QtCore/qglobal.h>
// #include <QtCore/qatomic.h>
// #include <QtCore/qhashfunctions.h>

#include <atomic>
#include <functional>

template <class T> class SharedDataPointer;

class SharedData
{
public:
    mutable std::atomic<int> ref;

    SharedData() noexcept : ref(0) { }
    SharedData(const SharedData &) noexcept : ref(0) { }

    // using the assignment operator would lead to corruption in the ref-counting
    SharedData &operator=(const SharedData &) = delete;
    ~SharedData() = default;
};

struct AdoptSharedDataTag { explicit constexpr AdoptSharedDataTag() = default; };

template <typename T>
class SharedDataPointer
{
public:
    typedef T Type;
    typedef T *pointer;

    void detach() { if (d && d->ref.load(std::memory_order_relaxed) != 1) detach_helper(); }
    T &operator*() { detach(); return *d; }
    const T &operator*() const { return *d; }
    T *operator->() { detach(); return d; }
    const T *operator->() const noexcept { return d; }
    operator T *() { detach(); return d; }
    operator const T *() const noexcept { return d; }
    T *data() { detach(); return d; }
    T *get() { detach(); return d; }
    const T *data() const noexcept { return d; }
    const T *get() const noexcept { return d; }
    const T *constData() const noexcept { return d; }
    T *take() noexcept { return std::exchange(d, nullptr); }

    [[nodiscard]]
    SharedDataPointer() noexcept : d(nullptr) { }
    //~SharedDataPointer() { if (d && !d->ref.deref()) delete d; }
    ~SharedDataPointer() {
        if (d && d->ref.fetch_sub(1, std::memory_order_acq_rel) == 1)
            delete d;
    }

    [[nodiscard]]
        explicit SharedDataPointer(T *data) noexcept : d(data)
    { if (d) d->ref.ref(); }
    [[nodiscard]]
    SharedDataPointer(T *data, AdoptSharedDataTag) noexcept : d(data)
    {}
    [[nodiscard]]
    SharedDataPointer(const SharedDataPointer &o) noexcept : d(o.d)
    //{ if (d) d->ref.ref(); }
    {
        if (d) d->ref.fetch_add(1, std::memory_order_relaxed);
    }

    void reset(T *ptr = nullptr) noexcept
    {
        if (ptr != d) {
            if (ptr)
                ptr->ref.fetch_add(1, std::memory_order_relaxed);
            T *old = std::exchange(d, ptr);
            if (old && old->ref.fetch_sub(1, std::memory_order_acq_rel) == 1)
                delete old;
        }
    }

    SharedDataPointer &operator=(const SharedDataPointer &o) noexcept
    {
        reset(o.d);
        return *this;
    }
    inline SharedDataPointer &operator=(T *o) noexcept
    {
        reset(o);
        return *this;
    }
    [[nodiscard]]
    SharedDataPointer(SharedDataPointer &&o) noexcept : d(std::exchange(o.d, nullptr)) {}
    //MOVE_ASSIGNMENT_OPERATOR_IMPL_VIA_MOVE_AND_SWAP
    SharedDataPointer &operator=(SharedDataPointer &&other) noexcept {
            SharedDataPointer moved(std::move(other));
            swap(moved);
            return *this;
    }
    operator bool () const noexcept { return d != nullptr; }
    bool operator!() const noexcept { return d == nullptr; }

    void swap(SharedDataPointer &other) noexcept
    { std::swap(d, other.d); }

#define DECLARE_COMPARE_SET(T1, A1, T2, A2) \
    friend bool operator<(T1, T2) noexcept \
    { return std::less<T*>{}(A1, A2); } \
        friend bool operator<=(T1, T2) noexcept \
    { return !std::less<T*>{}(A2, A1); } \
        friend bool operator>(T1, T2) noexcept \
    { return std::less<T*>{}(A2, A1); } \
        friend bool operator>=(T1, T2) noexcept \
    { return !std::less<T*>{}(A1, A2); } \
        friend bool operator==(T1, T2) noexcept \
    { return A1 == A2; } \
        friend bool operator!=(T1, T2) noexcept \
    { return A1 != A2; } \

        DECLARE_COMPARE_SET(const SharedDataPointer &p1, p1.d, const SharedDataPointer &p2, p2.d)
        DECLARE_COMPARE_SET(const SharedDataPointer &p1, p1.d, const T *ptr, ptr)
        DECLARE_COMPARE_SET(const T *ptr, ptr, const SharedDataPointer &p2, p2.d)
        DECLARE_COMPARE_SET(const SharedDataPointer &p1, p1.d, std::nullptr_t, nullptr)
        DECLARE_COMPARE_SET(std::nullptr_t, nullptr, const SharedDataPointer &p2, p2.d)

        protected:
                    T *clone();

private:
    void detach_helper();

    T *d;
};

template <typename T>
class ExplicitlySharedDataPointer
{
public:
    typedef T Type;
    typedef T *pointer;

    T &operator*() const { return *d; }
    T *operator->() noexcept { return d; }
    T *operator->() const noexcept { return d; }
    explicit operator T *() { return d; }
    explicit operator const T *() const noexcept { return d; }
    T *data() const noexcept { return d; }
    T *get() const noexcept { return d; }
    const T *constData() const noexcept { return d; }
    T *take() noexcept { return std::exchange(d, nullptr); }

    void detach() { if (d && d->ref.loadRelaxed() != 1) detach_helper(); }

    [[nodiscard]]
    ExplicitlySharedDataPointer() noexcept : d(nullptr) { }
    ~ExplicitlySharedDataPointer() { if (d && !d->ref.deref()) delete d; }

    [[nodiscard]]
        explicit ExplicitlySharedDataPointer(T *data) noexcept : d(data)
    { if (d) d->ref.ref(); }
    [[nodiscard]]
    ExplicitlySharedDataPointer(T *data, AdoptSharedDataTag) noexcept : d(data)
    {}
    [[nodiscard]]
    ExplicitlySharedDataPointer(const ExplicitlySharedDataPointer &o) noexcept : d(o.d)
    { if (d) d->ref.ref(); }

    template<typename X>
    [[nodiscard]]
    ExplicitlySharedDataPointer(const ExplicitlySharedDataPointer<X> &o) noexcept
#ifdef QT_ENABLE_QEXPLICITLYSHAREDDATAPOINTER_STATICCAST
        : d{(warnIfExplicitlySharedDataPointerStaticCastMacroDefined(), static_cast<T *>(o.data()))}
#else
        : d(o.data())
#endif
    { if (d) d->ref.ref(); }

    void reset(T *ptr = nullptr) noexcept
    {
        if (ptr != d) {
            if (ptr)
                ptr->ref.ref();
            T *old = std::exchange(d, ptr);
            if (old && !old->ref.deref())
                delete old;
        }
    }

    ExplicitlySharedDataPointer &operator=(const ExplicitlySharedDataPointer &o) noexcept
    {
        reset(o.d);
        return *this;
    }
    ExplicitlySharedDataPointer &operator=(T *o) noexcept
    {
        reset(o);
        return *this;
    }
    [[nodiscard]]
    ExplicitlySharedDataPointer(ExplicitlySharedDataPointer &&o) noexcept : d(std::exchange(o.d, nullptr)) {}
    // QT_MOVE_ASSIGNMENT_OPERATOR_IMPL_VIA_MOVE_AND_SWAP(ExplicitlySharedDataPointer)
    ExplicitlySharedDataPointer &operator=(ExplicitlySharedDataPointer &&other) noexcept {
        ExplicitlySharedDataPointer moved(std::move(other));
        swap(moved);
        return *this;
    }

    operator bool () const noexcept { return d != nullptr; }
    bool operator!() const noexcept { return d == nullptr; }

    void swap(ExplicitlySharedDataPointer &other) noexcept
    { qt_ptr_swap(d, other.d); }

    DECLARE_COMPARE_SET(const ExplicitlySharedDataPointer &p1, p1.d, const ExplicitlySharedDataPointer &p2, p2.d)
    DECLARE_COMPARE_SET(const ExplicitlySharedDataPointer &p1, p1.d, const T *ptr, ptr)
    DECLARE_COMPARE_SET(const T *ptr, ptr, const ExplicitlySharedDataPointer &p2, p2.d)
    DECLARE_COMPARE_SET(const ExplicitlySharedDataPointer &p1, p1.d, std::nullptr_t, nullptr)
    DECLARE_COMPARE_SET(std::nullptr_t, nullptr, const ExplicitlySharedDataPointer &p2, p2.d)

#undef DECLARE_COMPARE_SET

protected:
    T *clone();

private:
    void detach_helper();

#ifdef QT_ENABLE_QEXPLICITLYSHAREDDATAPOINTER_STATICCAST
    [[deprecated("Usage of QT_ENABLE_QEXPLICITLYSHAREDDATAPOINTER_STATICCAST is deprecated.")]]
    constexpr void warnIfExplicitlySharedDataPointerStaticCastMacroDefined() {}
#endif

    T *d;
};

// qcompilerdetection.h
#ifndef INLINE_TEMPLATE
#if defined(__GNUC__) || defined(__clang__)
# define INLINE_TEMPLATE inline __attribute__((__always_inline__))
#elif defined(_MSC_VER)
# define INLINE_TEMPLATE inline __forceinline
#else
# define INLINE_TEMPLATE inline
#endif
#endif
#ifndef OUTOFLINE_TEMPLATE
#  define OUTOFLINE_TEMPLATE
#endif

// Declared here and as Q_OUTOFLINE_TEMPLATE to work-around MSVC bug causing missing symbols at link time.
template <typename T>
INLINE_TEMPLATE T *SharedDataPointer<T>::clone()
{
    return new T(*d);
}

template <typename T>
OUTOFLINE_TEMPLATE void SharedDataPointer<T>::detach_helper()
{
    T *x = clone();
    x->ref.fetch_add(1, std::memory_order_relaxed);
    if (d->ref.fetch_sub(1, std::memory_order_acq_rel) == 1)
        delete d;
    d = x;
}

template <typename T>
INLINE_TEMPLATE T *ExplicitlySharedDataPointer<T>::clone()
{
    return new T(*d);
}

template <typename T>
OUTOFLINE_TEMPLATE void ExplicitlySharedDataPointer<T>::detach_helper()
{
    T *x = clone();
    x->ref.ref();
    if (!d->ref.deref())
        delete d;
    d = x;
}

template <typename T>
void swap(SharedDataPointer<T> &p1, SharedDataPointer<T> &p2) noexcept
{ p1.swap(p2); }

template <typename T>
void swap(ExplicitlySharedDataPointer<T> &p1, ExplicitlySharedDataPointer<T> &p2) noexcept
{ p1.swap(p2); }

template <typename T>
size_t qHash(const SharedDataPointer<T> &ptr, size_t seed = 0) noexcept
{
    return qHash(ptr.data(), seed);
}
template <typename T>
size_t qHash(const ExplicitlySharedDataPointer<T> &ptr, size_t seed = 0) noexcept
{
    return qHash(ptr.data(), seed);
}

// template<typename T> Q_DECLARE_TYPEINFO_BODY(SharedDataPointer<T>, Q_RELOCATABLE_TYPE);
// template<typename T> Q_DECLARE_TYPEINFO_BODY(ExplicitlySharedDataPointer<T>, Q_RELOCATABLE_TYPE);

// #define QT_DECLARE_QSDP_SPECIALIZATION_DTOR(Class) \
// template<> SharedDataPointer<Class>::~SharedDataPointer();

// #define QT_DECLARE_QSDP_SPECIALIZATION_DTOR_WITH_EXPORT(Class, ExportMacro) \
// template<> ExportMacro SharedDataPointer<Class>::~SharedDataPointer();

// #define QT_DEFINE_QSDP_SPECIALIZATION_DTOR(Class) \
// template<> SharedDataPointer<Class>::~SharedDataPointer() \
// { \
//         if (d && !d->ref.deref()) \
//         delete d; \
// }

// #define QT_DECLARE_QESDP_SPECIALIZATION_DTOR(Class) \
// template<> ExplicitlySharedDataPointer<Class>::~ExplicitlySharedDataPointer();

// #define QT_DECLARE_QESDP_SPECIALIZATION_DTOR_WITH_EXPORT(Class, ExportMacro) \
// template<> ExportMacro ExplicitlySharedDataPointer<Class>::~ExplicitlySharedDataPointer();

// #define QT_DEFINE_QESDP_SPECIALIZATION_DTOR(Class) \
// template<> ExplicitlySharedDataPointer<Class>::~ExplicitlySharedDataPointer() \
// { \
//         if (d && !d->ref.deref()) \
//         delete d; \
// }

#endif // SHAREDDATA_H
