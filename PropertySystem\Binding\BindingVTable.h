/**
 * @file PropertySystem/Binding/BindingVTable.h
 * @brief 现代化的绑定函数表实现，移除MetaType依赖
 * 
 * 这个文件提供了简化的BindingFunctionVTable实现，移除了不必要的MetaType参数，
 * 提高了性能并简化了接口。
 */

#ifndef PROPERTY_SYSTEM_BINDING_VTABLE_H
#define PROPERTY_SYSTEM_BINDING_VTABLE_H

#include <type_traits>
#include <utility>
#include <concepts>
#include <functional>
#include "../Core/Concepts.h"

namespace PropertySystem::Binding {

// ==================================================================================
// 前向声明
// ==================================================================================

namespace PropertySystem::Core {
    class UntypedPropertyData;
    template<typename T> class PropertyData;
}

using PropertySystem::Core::UntypedPropertyData;
using PropertySystem::Core::PropertyData;

// ==================================================================================
// 现代化的绑定函数表
// ==================================================================================

/**
 * @brief 现代化的绑定函数虚表，移除MetaType依赖
 * 
 * 这个结构提供了一个统一的接口来处理不同类型的可调用对象，
 * 相比原版本移除了不必要的MetaType参数，提高了性能。
 */
struct ModernBindingFunctionVTable {
    /**
     * @brief 调用函数指针类型
     * @param propertyData 属性数据指针
     * @param functor 可调用对象指针
     * @return 是否成功调用并更新了属性值
     */
    using CallFn = bool(*)(UntypedPropertyData *propertyData, void *functor);
    
    /**
     * @brief 析构函数指针类型
     * @param functor 要析构的可调用对象指针
     */
    using DestroyFn = void(*)(void *functor);
    
    /**
     * @brief 移动构造函数指针类型
     * @param addr 目标地址
     * @param other 源对象指针
     */
    using MoveConstructFn = void(*)(void *addr, void *other);
    
    /**
     * @brief 获取对象大小函数指针类型
     * @return 对象的大小（字节）
     */
    using SizeFn = std::size_t(*)();

    // ==================================================================================
    // 虚表成员
    // ==================================================================================
    
    CallFn call;                    ///< 调用函数
    DestroyFn destroy;              ///< 析构函数
    MoveConstructFn moveConstruct;  ///< 移动构造函数
    SizeFn size;                    ///< 获取大小函数

    // ==================================================================================
    // 静态工厂方法
    // ==================================================================================
    
    /**
     * @brief 为特定可调用对象类型创建虚表
     * @tparam Callable 可调用对象类型
     * @tparam PropertyType 属性类型（可选，用于类型检查）
     * @return 对应的虚表实例
     */
    template<typename Callable, typename PropertyType = void>
    requires std::invocable<Callable>
    static constexpr ModernBindingFunctionVTable createFor() {
        return ModernBindingFunctionVTable{
            // call 函数指针用于调用 Callable 对象并更新属性值。
            /*call*/[](UntypedPropertyData *propertyDataPtr, void *f) -> bool {
                Callable *callable = static_cast<Callable *>(f);
                
                if constexpr (!std::is_void_v<PropertyType>) {
                    // 有具体属性类型的情况
                    auto *propertyPtr = static_cast<PropertyData<PropertyType> *>(propertyDataPtr);
                    
                    if constexpr (std::is_void_v<std::invoke_result_t<Callable>>) {
                        // 无返回值的callable
                        (*callable)();
                        return false; // 没有更新属性值
                    } else {
                        // 有返回值的callable
                        auto newValue = (*callable)();
                        if constexpr (::PropertySystem::Core::EqualityComparable<PropertyType>) {
                            if (propertyPtr->valueBypassingBindings() == newValue) {
                                return false; // 值没有变化
                            }
                        }
                        propertyPtr->setValueBypassingBindings(std::move(newValue));
                        return true;
                    }
                } else {
                    // 未类型化的情况，只调用不更新
                    if constexpr (std::is_void_v<std::invoke_result_t<Callable>>) {
                        (*callable)();
                        return false;
                    } else {
                        (*callable)();
                        return true; // 假设有更新
                    }
                }
            },
            // destroy 函数指针用于销毁 Callable 对象。
            /*destroy*/[](void *f){ 
                static_cast<Callable *>(f)->~Callable(); 
            },
            // moveConstruct 函数指针用于移动构造 Callable 对象。
            /*moveConstruct*/[](void *addr, void *other){
                new (addr) Callable(std::move(*static_cast<Callable *>(other)));
            },
            // size 是 Callable 对象的大小。
            /*size*/[]() -> std::size_t { 
                return sizeof(Callable); 
            }
        };
    }
};

// ==================================================================================
// 模板变量定义
// ==================================================================================

/**
 * @brief 为特定可调用对象类型提供的虚表模板变量
 * @tparam Callable 可调用对象类型
 * @tparam PropertyType 属性类型（可选）
 */
template<typename Callable, typename PropertyType = void>
requires std::invocable<Callable>
inline constexpr ModernBindingFunctionVTable modernBindingFunctionVTable = 
    ModernBindingFunctionVTable::createFor<Callable, PropertyType>();

// ==================================================================================
// 绑定函数包装器
// ==================================================================================

/**
 * @brief 现代化的属性绑定函数包装器
 * 
 * 这个结构将可调用对象和对应的虚表组合在一起，
 * 提供类型擦除的统一接口。
 */
struct ModernPropertyBindingFunction {
    const ModernBindingFunctionVTable *vtable = nullptr;  ///< 虚表指针
    void *functor = nullptr;                              ///< 可调用对象指针

    // ==================================================================================
    // 构造函数
    // ==================================================================================
    
    /**
     * @brief 默认构造函数
     */
    constexpr ModernPropertyBindingFunction() = default;
    
    /**
     * @brief 构造函数
     * @param vt 虚表指针
     * @param f 可调用对象指针
     */
    constexpr ModernPropertyBindingFunction(const ModernBindingFunctionVTable *vt, void *f) 
        : vtable(vt), functor(f) {}

    // ==================================================================================
    // 操作方法
    // ==================================================================================
    
    /**
     * @brief 调用绑定函数
     * @param propertyData 属性数据指针
     * @return 是否成功更新了属性值
     */
    bool call(UntypedPropertyData *propertyData) const {
        if (vtable && functor) {
            return vtable->call(propertyData, functor);
        }
        return false;
    }
    
    /**
     * @brief 销毁可调用对象
     */
    void destroy() const {
        if (vtable && functor) {
            vtable->destroy(functor);
        }
    }
    
    /**
     * @brief 移动构造可调用对象
     * @param addr 目标地址
     */
    void moveConstruct(void *addr) const {
        if (vtable && functor) {
            vtable->moveConstruct(addr, functor);
        }
    }
    
    /**
     * @brief 获取可调用对象大小
     * @return 对象大小（字节）
     */
    std::size_t size() const {
        if (vtable) {
            return vtable->size();
        }
        return 0;
    }
    
    /**
     * @brief 检查是否有效
     * @return 如果有有效的虚表和函数指针则返回true
     */
    bool isValid() const noexcept {
        return vtable != nullptr && functor != nullptr;
    }
    
    /**
     * @brief 检查是否为空
     * @return 如果为空则返回true
     */
    bool isEmpty() const noexcept {
        return vtable == nullptr || functor == nullptr;
    }
};

// ==================================================================================
// 便利函数
// ==================================================================================

/**
 * @brief 创建类型化的绑定函数
 * @tparam PropertyType 属性类型
 * @tparam Callable 可调用对象类型
 * @param callable 可调用对象
 * @return 绑定函数包装器
 */
template<typename PropertyType, typename Callable>
requires ::PropertySystem::Core::PropertyBindingCallable<Callable, PropertyType>
constexpr ModernPropertyBindingFunction makeTypedBinding(Callable &&callable) {
    return {
        &modernBindingFunctionVTable<std::remove_reference_t<Callable>, PropertyType>,
        &callable
    };
}

/**
 * @brief 创建未类型化的绑定函数
 * @tparam Callable 可调用对象类型
 * @param callable 可调用对象
 * @return 绑定函数包装器
 */
template<typename Callable>
requires std::invocable<Callable>
constexpr ModernPropertyBindingFunction makeUntypedBinding(Callable &&callable) {
    return {
        &modernBindingFunctionVTable<std::remove_reference_t<Callable>, void>,
        &callable
    };
}

} // namespace PropertySystem::Binding

// ==================================================================================
// 全局命名空间别名
// ==================================================================================

using PropertySystem::Binding::ModernBindingFunctionVTable;
using PropertySystem::Binding::ModernPropertyBindingFunction;
using PropertySystem::Binding::modernBindingFunctionVTable;
using PropertySystem::Binding::makeTypedBinding;
using PropertySystem::Binding::makeUntypedBinding;

#endif // PROPERTY_SYSTEM_BINDING_VTABLE_H
