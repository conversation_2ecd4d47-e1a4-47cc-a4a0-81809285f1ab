/**
 * @file Core/UntypedPropertyBinding.h
 * @brief 未类型化属性绑定实现
 * 
 * 这个文件实现了Property系统的未类型化属性绑定，
 * 提供类型擦除的绑定接口和基础绑定功能。
 */

#ifndef PROPERTY_SYSTEM_CORE_UNTYPED_PROPERTY_BINDING_H
#define PROPERTY_SYSTEM_CORE_UNTYPED_PROPERTY_BINDING_H

#include "PropertyTypes.h"
#include "PropertyBindingError.h"
#include "PropertyBindingPrivate.h"
#include "../Binding/BindingLocation.h"
#include "../Binding/BindingVTable.h"
#include <memory>
#include <functional>

namespace PropertySystem::Core {

// ==================================================================================
// 未类型化属性绑定类
// ==================================================================================

/**
 * @brief 未类型化属性绑定类
 * 
 * 这个类提供类型擦除的属性绑定功能，是所有类型化绑定的基类。
 * 它封装了绑定的核心逻辑，包括绑定函数的存储、执行和错误处理。
 */
class UntypedPropertyBinding {
public:
    // ==================================================================================
    // 构造和析构
    // ==================================================================================
    
    /**
     * @brief 默认构造函数（创建空绑定）
     */
    UntypedPropertyBinding();
    
    /**
     * @brief 从绑定函数创建绑定
     * @param vtable 绑定函数虚表
     * @param function 绑定函数指针
     * @param location 源代码位置信息
     */
    UntypedPropertyBinding(const Binding::ModernBindingFunctionVTable *vtable, 
                          void *function, 
                          const PropertyBindingSourceLocation &location);
    
    /**
     * @brief 从可调用对象创建绑定
     * @tparam Functor 可调用对象类型
     * @param f 可调用对象
     * @param location 源代码位置信息
     */
    template<typename Functor>
    UntypedPropertyBinding(Functor &&f, const PropertyBindingSourceLocation &location)
        : UntypedPropertyBinding(&Binding::modernBindingFunctionVTable<std::remove_reference_t<Functor>>, 
                                &f, location) {}
    
    /**
     * @brief 拷贝构造函数
     * @param other 其他绑定对象
     */
    UntypedPropertyBinding(const UntypedPropertyBinding &other);
    
    /**
     * @brief 移动构造函数
     * @param other 其他绑定对象
     */
    UntypedPropertyBinding(UntypedPropertyBinding &&other) noexcept;
    
    /**
     * @brief 析构函数
     */
    ~UntypedPropertyBinding();
    
    /**
     * @brief 拷贝赋值操作符
     * @param other 其他绑定对象
     * @return 当前对象的引用
     */
    UntypedPropertyBinding &operator=(const UntypedPropertyBinding &other);
    
    /**
     * @brief 移动赋值操作符
     * @param other 其他绑定对象
     * @return 当前对象的引用
     */
    UntypedPropertyBinding &operator=(UntypedPropertyBinding &&other) noexcept;

    // ==================================================================================
    // 状态查询
    // ==================================================================================
    
    /**
     * @brief 检查绑定是否为空
     * @return 如果绑定为空则返回true
     */
    bool isNull() const;
    
    /**
     * @brief 检查绑定是否有效
     * @return 如果绑定有效则返回true
     */
    bool isValid() const;
    
    /**
     * @brief 获取绑定错误信息
     * @return 错误信息对象
     */
    PropertyBindingError error() const;
    
    /**
     * @brief 获取源代码位置信息
     * @return 源代码位置信息
     */
    PropertyBindingSourceLocation sourceLocation() const;

    // ==================================================================================
    // 绑定操作
    // ==================================================================================
    
    /**
     * @brief 交换两个绑定对象
     * @param other 其他绑定对象
     */
    void swap(UntypedPropertyBinding &other) noexcept;
    
    /**
     * @brief 重置绑定为空
     */
    void reset();

    // ==================================================================================
    // 比较操作符
    // ==================================================================================
    
    /**
     * @brief 相等比较操作符
     * @param other 其他绑定对象
     * @return 如果相等则返回true
     */
    bool operator==(const UntypedPropertyBinding &other) const;
    
    /**
     * @brief 不等比较操作符
     * @param other 其他绑定对象
     * @return 如果不等则返回true
     */
    bool operator!=(const UntypedPropertyBinding &other) const {
        return !(*this == other);
    }

    // ==================================================================================
    // 内部接口
    // ==================================================================================
    
protected:
    /**
     * @brief 从私有数据构造（内部使用）
     * @param priv 私有数据指针
     */
    explicit UntypedPropertyBinding(Internal::PropertyBindingPrivate *priv);
    
    /**
     * @brief 获取私有数据指针
     * @return 私有数据指针
     */
    Internal::PropertyBindingPrivate* d() const { return d_.get(); }

private:
    // ==================================================================================
    // 私有成员变量
    // ==================================================================================
    
    Internal::PropertyBindingPrivatePtr d_;  ///< 私有数据的智能指针

    // ==================================================================================
    // 友元声明
    // ==================================================================================
    
    friend class Internal::PropertyBindingData;
    friend class Internal::PropertyBindingPrivate;
    template<typename T> friend class PropertyBinding;
    template<typename T> friend class Property;
};

// ==================================================================================
// 类型化属性绑定模板类
// ==================================================================================

/**
 * @brief 类型化属性绑定模板类
 * @tparam PropertyType 属性类型
 * 
 * 这个类提供类型安全的属性绑定，确保绑定函数的返回类型
 * 与属性类型匹配。
 */
template<typename PropertyType>
requires PropertySystem::Core::PropertyType<PropertyType>
class PropertyBinding : public UntypedPropertyBinding {
public:
    using value_type = PropertyType;
    
    /**
     * @brief 默认构造函数
     */
    PropertyBinding() = default;
    
    /**
     * @brief 从可调用对象创建绑定
     * @tparam Functor 可调用对象类型
     * @param f 可调用对象
     * @param location 源代码位置信息
     */
    template<typename Functor>
    requires PropertyBindingCallable<Functor, PropertyType>
    PropertyBinding(Functor &&f, const PropertyBindingSourceLocation &location = PROPERTY_DEFAULT_BINDING_LOCATION)
        : UntypedPropertyBinding(&Binding::modernBindingFunctionVTable<std::remove_reference_t<Functor>, PropertyType>, 
                                &f, location) {}
    
    /**
     * @brief 从未类型化绑定创建（如果类型匹配）
     * @param other 未类型化绑定
     */
    PropertyBinding(const UntypedPropertyBinding &other)
        : UntypedPropertyBinding(other) {}
    
    /**
     * @brief 从未类型化绑定移动创建（如果类型匹配）
     * @param other 未类型化绑定
     */
    PropertyBinding(UntypedPropertyBinding &&other)
        : UntypedPropertyBinding(std::move(other)) {}
};

// ==================================================================================
// 便利函数
// ==================================================================================

/**
 * @brief 创建属性绑定的便利函数
 * @tparam Functor 可调用对象类型
 * @param f 绑定函数
 * @param location 源代码位置信息
 * @return 类型化的属性绑定对象
 */
template<typename Functor>
requires std::invocable<Functor> && PropertySystem::Core::PropertyType<std::invoke_result_t<Functor>>
auto makePropertyBinding(Functor &&f, const PropertyBindingSourceLocation &location = PROPERTY_DEFAULT_BINDING_LOCATION) {
    return PropertyBinding<std::invoke_result_t<Functor>>(std::forward<Functor>(f), location);
}

/**
 * @brief 从属性创建绑定的便利函数
 * @tparam PropertyType 属性类型
 * @param otherProperty 其他属性
 * @param location 源代码位置信息
 * @return 属性绑定对象
 */
template<typename PropertyType>
PropertyBinding<PropertyType> makePropertyBinding(const Property<PropertyType> &otherProperty,
                                                  const PropertyBindingSourceLocation &location = PROPERTY_DEFAULT_BINDING_LOCATION) {
    return makePropertyBinding([&otherProperty]() -> PropertyType { 
        return otherProperty.value(); 
    }, location);
}

// ==================================================================================
// 非成员函数
// ==================================================================================

/**
 * @brief 交换两个绑定对象
 * @param lhs 第一个绑定对象
 * @param rhs 第二个绑定对象
 */
inline void swap(UntypedPropertyBinding &lhs, UntypedPropertyBinding &rhs) noexcept {
    lhs.swap(rhs);
}

} // namespace PropertySystem::Core

// ==================================================================================
// 全局命名空间别名
// ==================================================================================

using PropertySystem::Core::UntypedPropertyBinding;
using PropertySystem::Core::PropertyBinding;
using PropertySystem::Core::makePropertyBinding;

#endif // PROPERTY_SYSTEM_CORE_UNTYPED_PROPERTY_BINDING_H
