#ifndef MODULARITY_IMODULESETUP_H
#define MODULARITY_IMODULESETUP_H

#include <string>

namespace modularity {
class IModuleSetup
{
public:

    virtual ~IModuleSetup() {}

    virtual std::string moduleName() const = 0;

    virtual void registerExports() {}
    virtual void resolveImports() {}

    virtual void registerResources() {}
    virtual void registerUiTypes() {}

    virtual void onPreInit() {}
    virtual void onInit() {}
    virtual void onAllInited() {}
    virtual void onDelayedInit() {}
    virtual void onDeinit() {}
    virtual void onDestroy() {}

    virtual void onStartApp() {}
};
}

#endif // MODULARITY_IMODULESETUP_H
