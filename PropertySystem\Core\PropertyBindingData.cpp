/**
 * @file Core/PropertyBindingData.cpp
 * @brief PropertyBindingData类的实现
 * 
 * 这个文件实现了PropertyBindingData类的核心功能，
 * 包括绑定管理、观察者通知和依赖追踪。
 */

#include "PropertyBindingData.h"
#include "PropertyBindingPrivate.h"
#include "UntypedPropertyBinding.h"
#include "BindingEvaluationState.h"
#include "PropertyObserverSimple.h"
#include <atomic>
#include <cassert>

namespace PropertySystem::Core::Internal {

// ==================================================================================
// 全局状态管理
// ==================================================================================

namespace {
    thread_local BindingStatus* g_current_binding_status = nullptr;
    
    BindingStatus& getCurrentBindingStatus() {
        if (!g_current_binding_status) {
            static thread_local BindingStatus status;
            g_current_binding_status = &status;
        }
        return *g_current_binding_status;
    }
}

// ==================================================================================
// PropertyBindingData实现
// ==================================================================================

PropertyBindingData::PropertyBindingData() = default;

PropertyBindingData::PropertyBindingData(PropertyBindingData &&other) noexcept {
    d_ptr_.store(other.d_ptr_.exchange(0));
    fixupAfterMove();
}

PropertyBindingData::~PropertyBindingData() {
    removeBinding();
    clearObservers();
}

bool PropertyBindingData::hasBinding() const {
    std::uintptr_t d = d_ptr_.load();
    return (d & BindingBit) && (reinterpret_cast<PropertyBindingPrivate*>(d & PointerMask) != nullptr);
}

UntypedPropertyBinding PropertyBindingData::setBinding(const UntypedPropertyBinding &newBinding,
                                                      UntypedPropertyData *propertyDataPtr) {
    // 获取旧绑定
    UntypedPropertyBinding oldBinding;
    if (hasBinding()) {
        oldBinding = UntypedPropertyBinding(binding());
    }
    
    // 移除旧绑定
    removeBinding();
    
    // 设置新绑定
    if (!newBinding.isNull()) {
        auto* bindingPrivate = newBinding.d_.get();
        if (bindingPrivate) {
            bindingPrivate->setProperty(propertyDataPtr);
            setBindingHelper(bindingPrivate);
        }
    }
    
    return oldBinding;
}

PropertyBindingPrivate* PropertyBindingData::binding() const {
    std::uintptr_t d = d_ptr_.load();
    if (d & BindingBit) {
        return reinterpret_cast<PropertyBindingPrivate*>(d & PointerMask);
    }
    return nullptr;
}

void PropertyBindingData::removeBinding() {
    std::uintptr_t d = d_ptr_.load();
    if (d & BindingBit) {
        auto* bindingPrivate = reinterpret_cast<PropertyBindingPrivate*>(d & PointerMask);
        if (bindingPrivate) {
            bindingPrivate->setProperty(nullptr);
        }
        d_ptr_.store(0);
    }
}

void PropertyBindingData::removeBindingUnlessInWrapper() {
    // 简化实现：直接移除绑定
    // 在完整实现中需要检查是否在绑定包装器中
    removeBinding();
}

void PropertyBindingData::registerWithCurrentlyEvaluatingBinding() const {
    BindingStatus& status = getCurrentBindingStatus();
    if (status.currently_evaluating_binding) {
        registerWithCurrentlyEvaluatingBinding(status.currently_evaluating_binding);
    }
}

void PropertyBindingData::registerWithCurrentlyEvaluatingBinding(BindingEvaluationState *currentBinding) const {
    if (!currentBinding) return;
    
    registerDependencyHelper(currentBinding);
}

void PropertyBindingData::notifyObservers(UntypedPropertyData *propertyDataPtr) const {
    // 简化实现：遍历观察者链表并通知
    PropertyObserver* observer = firstObserver();
    while (observer) {
        // 通知观察者
        if (observer->changeHandler) {
            observer->changeHandler(observer, propertyDataPtr);
        }
        observer = observer->next.data();
    }
}

void PropertyBindingData::addObserver(PropertyObserver *observer) {
    if (!observer) return;
    
    // 将观察者添加到链表头部
    observer->next = firstObserver();
    if (observer->next.data()) {
        observer->next.data()->prev = &observer->next;
    }
    setFirstObserver(observer);
    observer->prev = reinterpret_cast<PropertyObserver**>(&d_ptr_);
}

void PropertyBindingData::removeObserver(PropertyObserver *observer) {
    if (!observer) return;
    
    // 从链表中移除观察者
    if (observer->next.data()) {
        observer->next.data()->prev = observer->prev;
    }
    if (observer->prev) {
        observer->prev.setPointer(observer->next.data());
    }
    observer->next = nullptr;
    observer->prev.clear();
}

bool PropertyBindingData::isNotificationDelayed() const {
    std::uintptr_t d = d_ptr_.load();
    return d & DelayedNotificationBit;
}

size_t PropertyBindingData::observerCount() const {
    size_t count = 0;
    PropertyObserver* observer = firstObserver();
    while (observer) {
        ++count;
        observer = observer->next.data();
    }
    return count;
}

std::uintptr_t& PropertyBindingData::d_ref() const {
    // 注意：这是一个危险的操作，仅用于兼容性
    return const_cast<std::atomic<std::uintptr_t>&>(d_ptr_);
}

std::uintptr_t PropertyBindingData::d() const {
    return d_ptr_.load();
}

// ==================================================================================
// 私有辅助方法
// ==================================================================================

void PropertyBindingData::setBindingHelper(PropertyBindingPrivate *binding) {
    if (binding) {
        std::uintptr_t newValue = reinterpret_cast<std::uintptr_t>(binding) | BindingBit;
        d_ptr_.store(newValue);
    }
}

void PropertyBindingData::removeBindingHelper() {
    removeBinding();
}

void PropertyBindingData::registerDependencyHelper(BindingEvaluationState *currentBinding) const {
    // 简化实现：记录依赖关系
    // 在完整实现中需要检查循环依赖等
    if (currentBinding && currentBinding->binding) {
        // 创建观察者来监听这个属性的变化
        // 当属性变化时通知绑定重新评估
    }
}

PropertyObserver* PropertyBindingData::firstObserver() const {
    std::uintptr_t d = d_ptr_.load();
    if (d & BindingBit) {
        // 如果有绑定，观察者存储在绑定对象中
        auto* bindingPrivate = reinterpret_cast<PropertyBindingPrivate*>(d & PointerMask);
        return bindingPrivate ? bindingPrivate->firstObserver() : nullptr;
    } else {
        // 直接存储观察者指针
        return reinterpret_cast<PropertyObserver*>(d & PointerMask);
    }
}

void PropertyBindingData::setFirstObserver(PropertyObserver *observer) {
    std::uintptr_t d = d_ptr_.load();
    if (d & BindingBit) {
        // 如果有绑定，观察者存储在绑定对象中
        auto* bindingPrivate = reinterpret_cast<PropertyBindingPrivate*>(d & PointerMask);
        if (bindingPrivate) {
            bindingPrivate->setFirstObserver(observer);
        }
    } else {
        // 直接存储观察者指针
        std::uintptr_t newValue = reinterpret_cast<std::uintptr_t>(observer);
        d_ptr_.store(newValue);
    }
}

void PropertyBindingData::clearObservers() {
    PropertyObserver* observer = firstObserver();
    while (observer) {
        PropertyObserver* next = observer->next.data();
        removeObserver(observer);
        observer = next;
    }
}

void PropertyBindingData::fixupAfterMove() {
    // 修复移动后的指针引用
    PropertyObserver* observer = firstObserver();
    if (observer) {
        observer->prev = reinterpret_cast<PropertyObserver**>(&d_ptr_);
    }
}

} // namespace PropertySystem::Core::Internal
