﻿#include "buffer.h"
#include <algorithm>

namespace filesystem {

Buffer::Buffer() : m_pos(0) {
    setOpenMode(OpenMode::NotOpen);
}

Buffer::Buffer(const std::vector<char>& data) : m_buffer(data), m_pos(0) {
    setOpenMode(OpenMode::ReadWrite);
}

Buffer::Buffer(std::vector<char>&& data) : m_buffer(std::move(data)), m_pos(0) {
    setOpenMode(OpenMode::ReadWrite);
}

Buffer::Buffer(const char* data, int64_t size) : m_pos(0) {
    m_buffer.assign(data, data + size);
    setOpenMode(OpenMode::ReadWrite);
}

Buffer::Buffer(const std::string& data) : m_pos(0) {
    m_buffer.assign(data.begin(), data.end());
    setOpenMode(OpenMode::ReadWrite);
}

Buffer::~Buffer() = default;

bool Buffer::open(OpenMode mode) {
    setOpenMode(mode);
    m_pos = 0;

    // Use static_cast to perform binary operation with enum class
    if ((static_cast<int>(mode) & static_cast<int>(OpenMode::Truncate)) != 0) {
        m_buffer.clear();
    }

    return true;
}

void Buffer::close() {
    setOpenMode(OpenMode::NotOpen);
}

bool Buffer::isOpen() const {
    return openMode() != OpenMode::NotOpen;
}

IODevice::OpenMode Buffer::openMode() const {
    return IODevice::openMode();
}

int64_t Buffer::size() const {
    return m_buffer.size();
}

int64_t Buffer::pos() const {
    return m_pos;
}

int64_t Buffer::seek(int64_t pos, SeekOrigin origin) {
    int64_t newPos = 0;

    switch (origin) {
    case SeekOrigin::Begin:
        newPos = pos;
        break;
    case SeekOrigin::Current:
        newPos = m_pos + pos;
        break;
    case SeekOrigin::End:
        newPos = m_buffer.size() + pos;
        break;
    }

    if (newPos < 0) {
        m_errorString = "Seek position out of range";
        return -1;
    }

    m_pos = newPos;
    return m_pos;
}

bool Buffer::atEnd() const {
    return m_pos >= static_cast<int64_t>(m_buffer.size());
}

int64_t Buffer::read(char* data, int64_t maxSize) {
    // Use static_cast to perform binary operation with enum class
    if (!isOpen() || (static_cast<int>(openMode()) & static_cast<int>(OpenMode::ReadOnly)) == 0) {
        m_errorString = "Buffer not open for reading";
        return -1;
    }

    if (atEnd()) {
        return 0;
    }

    int64_t bytesToRead = std::min(maxSize, static_cast<int64_t>(m_buffer.size()) - m_pos);
    std::copy(m_buffer.begin() + m_pos, m_buffer.begin() + m_pos + bytesToRead, data);
    m_pos += bytesToRead;

    return bytesToRead;
}

int64_t Buffer::write(const char* data, int64_t size) {
    // Use static_cast to perform binary operation with enum class
    if (!isOpen() || (static_cast<int>(openMode()) & static_cast<int>(OpenMode::WriteOnly)) == 0) {
        m_errorString = "Buffer not open for writing";
        return -1;
    }

    if (size <= 0) {
        return 0;
    }

    // If we're in append mode, move to the end
    if ((static_cast<int>(openMode()) & static_cast<int>(OpenMode::Append)) != 0) {
        m_pos = m_buffer.size();
    }

    // If we're writing past the end, resize the buffer
    if (m_pos + size > static_cast<int64_t>(m_buffer.size())) {
        m_buffer.resize(m_pos + size);
    }

    // Copy the data
    std::copy(data, data + size, m_buffer.begin() + m_pos);
    m_pos += size;

    return size;
}

bool Buffer::flush() {
    return true;
}

std::string Buffer::errorString() const {
    return m_errorString;
}

const std::vector<char>& Buffer::data() const {
    return m_buffer;
}

void Buffer::setData(const std::vector<char>& data) {
    m_buffer = data;
    m_pos = std::min(m_pos, static_cast<int64_t>(m_buffer.size()));
}

void Buffer::setData(std::vector<char>&& data) {
    m_buffer = std::move(data);
    m_pos = std::min(m_pos, static_cast<int64_t>(m_buffer.size()));
}

void Buffer::setData(const char* data, int64_t size) {
    m_buffer.assign(data, data + size);
    m_pos = std::min(m_pos, static_cast<int64_t>(m_buffer.size()));
}

void Buffer::setData(const std::string& data) {
    m_buffer.assign(data.begin(), data.end());
    m_pos = std::min(m_pos, static_cast<int64_t>(m_buffer.size()));
}

void Buffer::clear() {
    m_buffer.clear();
    m_pos = 0;
}

void Buffer::resize(int64_t size) {
    m_buffer.resize(size);
    m_pos = std::min(m_pos, size);
}

void Buffer::reserve(int64_t size) {
    m_buffer.reserve(size);
}

} // namespace filesystem
