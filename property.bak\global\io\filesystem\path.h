﻿#ifndef PATH_H
#define PATH_H

#include <string>
#include <vector>

namespace filesystem {

/**
 * @brief The Path class provides platform-independent path handling.
 * 
 * This class handles path operations in a platform-independent way,
 * supporting both Windows and Unix-style paths.
 */
class Path {
public:
    /**
     * @brief Constructs an empty path.
     */
    Path() = default;

    /**
     * @brief Constructs a path from a string.
     * @param path The path string.
     */
    explicit Path(const std::string& path);

    /**
     * @brief Constructs a path from a wide string.
     * @param path The wide path string.
     */
    explicit Path(const std::wstring& path);

    /**
     * @brief Copy constructor.
     */
    Path(const Path& other);

    /**
     * @brief Move constructor.
     */
    Path(Path&& other) noexcept;

    /**
     * @brief Assignment operator.
     */
    Path& operator=(const Path& other);

    /**
     * @brief Move assignment operator.
     */
    Path& operator=(Path&& other) noexcept;

    /**
     * @brief Destructor.
     */
    ~Path() = default;

    /**
     * @brief Returns the path as a string.
     * @return The path string.
     */
    std::string toString() const;

    /**
     * @brief Returns the path as a wide string.
     * @return The path wide string.
     */
    std::wstring toWString() const;

    /**
     * @brief Returns the native path string (with platform-specific separators).
     * @return The native path string.
     */
    std::string toNativeString() const;

    /**
     * @brief Returns the native path wide string (with platform-specific separators).
     * @return The native path wide string.
     */
    std::wstring toNativeWString() const;

    /**
     * @brief Returns whether the path is absolute.
     * @return True if the path is absolute, false otherwise.
     */
    bool isAbsolute() const;

    /**
     * @brief Returns whether the path is relative.
     * @return True if the path is relative, false otherwise.
     */
    bool isRelative() const;

    /**
     * @brief Returns the file name component of the path.
     * @return The file name.
     */
    std::string fileName() const;

    /**
     * @brief Returns the base name component of the path (file name without extension).
     * @return The base name.
     */
    std::string baseName() const;

    /**
     * @brief Returns the file extension.
     * @return The file extension (without the dot).
     */
    std::string extension() const;

    /**
     * @brief Returns the parent directory path.
     * @return The parent directory path.
     */
    Path parentPath() const;

    /**
     * @brief Returns whether the path exists.
     * @return True if the path exists, false otherwise.
     */
    bool exists() const;

    /**
     * @brief Returns whether the path points to a file.
     * @return True if the path points to a file, false otherwise.
     */
    bool isFile() const;

    /**
     * @brief Returns whether the path points to a directory.
     * @return True if the path points to a directory, false otherwise.
     */
    bool isDirectory() const;

    /**
     * @brief Joins this path with another path.
     * @param other The path to join.
     * @return The joined path.
     */
    Path join(const Path& other) const;

    /**
     * @brief Joins this path with a string.
     * @param other The string to join.
     * @return The joined path.
     */
    Path join(const std::string& other) const;

    /**
     * @brief Normalizes the path (resolves ".." and "." components).
     * @return The normalized path.
     */
    Path normalize() const;

    /**
     * @brief Makes the path absolute.
     * @return The absolute path.
     */
    Path makeAbsolute() const;

    /**
     * @brief Makes the path relative to another path.
     * @param base The base path.
     * @return The relative path.
     */
    Path makeRelative(const Path& base) const;

    /**
     * @brief Corrects the case of the path (for case-insensitive filesystems).
     * @return The case-corrected path.
     */
    Path correctCase() const;

    /**
     * @brief Splits the path into components.
     * @return The path components.
     */
    std::vector<std::string> components() const;

    /**
     * @brief Returns the root component of the path.
     * @return The root component.
     */
    std::string root() const;

    /**
     * @brief Returns the drive letter (Windows only).
     * @return The drive letter or empty string on non-Windows platforms.
     */
    std::string drive() const;

    /**
     * @brief Returns whether the path is a UNC path (Windows only).
     * @return True if the path is a UNC path, false otherwise.
     */
    bool isUncPath() const;

private:
    std::wstring m_path;

    static std::wstring stringToWString(const std::string& str);
    static std::string wstringToString(const std::wstring& wstr);
};

} // namespace filesystem

#endif // PATH_H
