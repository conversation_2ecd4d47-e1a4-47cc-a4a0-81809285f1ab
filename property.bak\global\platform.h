﻿#pragma once

#ifndef PLATFORM_H
#define PLATFORM_H

#include <string>
#include "standardpaths.h"

#ifdef QT_CORE_LIB
#include <QStandardPaths>
#endif
#ifdef _WIN32
#include <windows.h>
#include <shlobj.h>
#include <locale>
#include <codecvt>
#else
#include <unistd.h>
#include <sys/types.h>
#include <pwd.h>
#endif

static std::string get_user_directory() {
#ifdef QT_CORE_LIB
    return QStandardPaths::writableLocation(QStandardPaths::HomeLocation)
               .toStdString() + "/";
#elif _WIN32
    wchar_t* path = nullptr;
    if (SHGetKnownFolderPath(FOLDERID_Profile, 0, nullptr, &path) == S_OK) {
        std::wstring_convert<std::codecvt_utf8_utf16<wchar_t>> converter;
        std::string dir = converter.to_bytes(path);
        CoTaskMemFree(path); // release memory.
        return dir;
    }
    if (auto dir = std::getenv("USERPROFILE")) {
        return dir;
    }
    return "";
#else  // Linux/macOS
    const char* dir = std::getenv("HOME");
    if (dir == nullptr) {
        struct passwd* pw = getpwuid(getuid());
        if (pw) {
            dir = pw->pw_dir;
        } else {
            return "";
        }
    }
    return dir;
#endif
}

// Delayed initialization after QApplication created.
#define HOME_DIR (HOME_DIR_impl())
inline const std::string& HOME_DIR_impl() {
    static const std::string instance = get_user_directory();
    return instance;
}
#define DOC_DIR (DOC_DIR_impl())
inline const std::string& DOC_DIR_impl() {
    // static const auto instance = QStandardPaths::writableLocation(
    //                                         QStandardPaths::DocumentsLocation).toStdString() + "/";
    static const auto instance = StandardPaths::getPath(StandardPaths::DirectoryType::Documents) + "/";
    return instance;
}

#define DATA_DIR (DATA_DIR_impl())
inline const std::string& DATA_DIR_impl() {
    static const std::string instance = DOC_DIR + "ProjectA/";
    return instance;
}

const std::string WORKSPACES_DIR = DATA_DIR + "workspaces/";
const std::string CONFIG_DIR = DATA_DIR + "config/";
const std::string LOG_DIR = DATA_DIR + "logs/";

const std::string SERVER_NAME = "ProjectA-Alpha";
const std::string DEFAULT_DATABASE_NAME = "ProjectA-Alpha";

#endif // PLATFORM_H
