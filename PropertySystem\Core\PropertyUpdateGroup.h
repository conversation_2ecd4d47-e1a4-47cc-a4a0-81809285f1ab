/**
 * @file Core/PropertyUpdateGroup.h
 * @brief 属性更新组管理
 * 
 * 这个文件实现了Property系统的更新组管理，
 * 提供批量更新和延迟通知功能。
 */

#ifndef PROPERTY_SYSTEM_CORE_PROPERTY_UPDATE_GROUP_H
#define PROPERTY_SYSTEM_CORE_PROPERTY_UPDATE_GROUP_H

#include "PropertyTypes.h"
#include <memory>
#include <atomic>

namespace PropertySystem::Core {

// ==================================================================================
// 前向声明
// ==================================================================================

namespace Internal {
    struct PropertyDelayedNotifications;
    class PropertyBindingData;
}

// ==================================================================================
// 属性更新组管理
// ==================================================================================

/**
 * @brief 开始属性更新组
 * 
 * 在更新组中，属性的变化不会立即触发通知，
 * 而是延迟到更新组结束时批量处理。
 */
void beginPropertyUpdateGroup();

/**
 * @brief 结束属性更新组
 * 
 * 结束最外层更新组时，会处理所有延迟的通知。
 */
void endPropertyUpdateGroup();

/**
 * @brief 检查是否在更新组中
 * @return 如果在更新组中则返回true
 */
bool isInPropertyUpdateGroup();

/**
 * @brief 获取当前更新组的嵌套深度
 * @return 嵌套深度
 */
int getPropertyUpdateGroupDepth();

// ==================================================================================
// RAII属性更新组类
// ==================================================================================

/**
 * @brief RAII属性更新组类
 * 
 * 这个类使用RAII模式管理属性更新组的生命周期，
 * 构造时开始更新组，析构时结束更新组。
 */
class ScopedPropertyUpdateGroup {
public:
    /**
     * @brief 构造函数
     * 
     * 自动开始一个属性更新组
     */
    ScopedPropertyUpdateGroup() {
        beginPropertyUpdateGroup();
    }
    
    /**
     * @brief 析构函数
     * 
     * 自动结束属性更新组
     */
    ~ScopedPropertyUpdateGroup() noexcept(false) {
        endPropertyUpdateGroup();
    }
    
    // 禁用拷贝和移动
    ScopedPropertyUpdateGroup(const ScopedPropertyUpdateGroup&) = delete;
    ScopedPropertyUpdateGroup(ScopedPropertyUpdateGroup&&) = delete;
    ScopedPropertyUpdateGroup& operator=(const ScopedPropertyUpdateGroup&) = delete;
    ScopedPropertyUpdateGroup& operator=(ScopedPropertyUpdateGroup&&) = delete;
};

// ==================================================================================
// 延迟通知管理
// ==================================================================================

namespace Internal {

/**
 * @brief 属性延迟通知数据结构
 * 
 * 管理延迟通知的属性列表和相关数据。
 */
struct PropertyDelayedNotifications {
    // 页面大小常量（避免分配超过一页内存）
    static constexpr size_t PageSize = 4096;
    
    // 引用计数
    std::atomic<int> ref_count{0};
    
    // 链表指针（用于处理超过单页大小的情况）
    std::unique_ptr<PropertyDelayedNotifications> next;
    
    // 当前使用的槽位数量
    size_t used = 0;
    
    // 计算每页可存储的代理数据数量
    static constexpr size_t capacity = 
        (PageSize - sizeof(std::atomic<int>) - sizeof(std::unique_ptr<PropertyDelayedNotifications>) - sizeof(size_t)) 
        / sizeof(void*) / 3; // 每个代理数据需要3个指针大小的空间
    
    // 延迟属性数据数组
    struct ProxyData {
        std::uintptr_t d_ptr;                           ///< 原始d_ptr值
        const PropertyBindingData *original_binding_data; ///< 原始绑定数据
        UntypedPropertyData *property_data;             ///< 属性数据指针
    };
    
    ProxyData delayed_properties[capacity];
    
    /**
     * @brief 构造函数
     */
    PropertyDelayedNotifications() = default;
    
    /**
     * @brief 析构函数
     */
    ~PropertyDelayedNotifications() = default;
    
    /**
     * @brief 增加引用计数
     */
    void addRef() {
        ref_count.fetch_add(1, std::memory_order_relaxed);
    }
    
    /**
     * @brief 减少引用计数
     * @return 如果引用计数不为0则返回true
     */
    bool deref() {
        return ref_count.fetch_sub(1, std::memory_order_acq_rel) != 1;
    }
    
    /**
     * @brief 添加延迟属性
     * @param bindingData 绑定数据
     * @param propertyData 属性数据
     */
    void addProperty(const PropertyBindingData *bindingData, UntypedPropertyData *propertyData);
    
    /**
     * @brief 评估指定索引的绑定
     * @param index 索引
     * @param bindingObservers 绑定观察者列表
     * @param status 绑定状态
     */
    void evaluateBindings(size_t index, 
                         std::vector<class PropertyObserver*> &bindingObservers, 
                         struct BindingEvaluationState *status);
    
    /**
     * @brief 通知指定索引的观察者
     * @param index 索引
     */
    void notify(size_t index);
    
    /**
     * @brief 清理指定索引的数据
     * @param index 索引
     */
    void cleanup(size_t index);

private:
    // 禁用拷贝和移动
    PropertyDelayedNotifications(const PropertyDelayedNotifications&) = delete;
    PropertyDelayedNotifications(PropertyDelayedNotifications&&) = delete;
    PropertyDelayedNotifications& operator=(const PropertyDelayedNotifications&) = delete;
    PropertyDelayedNotifications& operator=(PropertyDelayedNotifications&&) = delete;
};

/**
 * @brief 获取当前延迟通知对象
 * @return 延迟通知对象指针
 */
PropertyDelayedNotifications* getCurrentDelayedNotifications();

/**
 * @brief 设置当前延迟通知对象
 * @param notifications 延迟通知对象指针
 */
void setCurrentDelayedNotifications(PropertyDelayedNotifications *notifications);

/**
 * @brief 处理所有延迟通知
 */
void processDelayedNotifications();

} // namespace Internal

// ==================================================================================
// 便利函数
// ==================================================================================

/**
 * @brief 创建作用域更新组的便利函数
 * @return ScopedPropertyUpdateGroup对象
 */
inline ScopedPropertyUpdateGroup makeUpdateGroup() {
    return ScopedPropertyUpdateGroup();
}

/**
 * @brief 在更新组中执行函数
 * @tparam Func 函数类型
 * @param func 要执行的函数
 * @return 函数的返回值
 */
template<typename Func>
auto withUpdateGroup(Func &&func) -> decltype(func()) {
    ScopedPropertyUpdateGroup group;
    return func();
}

/**
 * @brief 在更新组中执行void函数
 * @tparam Func 函数类型
 * @param func 要执行的函数
 */
template<typename Func>
requires std::is_void_v<std::invoke_result_t<Func>>
void withUpdateGroup(Func &&func) {
    ScopedPropertyUpdateGroup group;
    func();
}

// ==================================================================================
// 全局状态管理
// ==================================================================================

namespace Internal {

/**
 * @brief 更新组状态结构
 */
struct UpdateGroupState {
    std::atomic<int> depth{0};                                    ///< 嵌套深度
    std::unique_ptr<PropertyDelayedNotifications> notifications; ///< 延迟通知对象
    
    /**
     * @brief 获取全局更新组状态
     * @return 更新组状态引用
     */
    static UpdateGroupState& global();
};

} // namespace Internal

} // namespace PropertySystem::Core

// ==================================================================================
// 全局命名空间别名
// ==================================================================================

using PropertySystem::Core::beginPropertyUpdateGroup;
using PropertySystem::Core::endPropertyUpdateGroup;
using PropertySystem::Core::isInPropertyUpdateGroup;
using PropertySystem::Core::getPropertyUpdateGroupDepth;
using PropertySystem::Core::ScopedPropertyUpdateGroup;
using PropertySystem::Core::makeUpdateGroup;
using PropertySystem::Core::withUpdateGroup;

#endif // PROPERTY_SYSTEM_CORE_PROPERTY_UPDATE_GROUP_H
