/**
 * @file Observer/PropertyObserver.h
 * @brief 属性观察者系统
 * 
 * 这个文件实现了Property系统的观察者模式，
 * 提供属性变化的监听和通知功能。
 */

#ifndef PROPERTY_SYSTEM_OBSERVER_PROPERTY_OBSERVER_H
#define PROPERTY_SYSTEM_OBSERVER_PROPERTY_OBSERVER_H

#include "../Core/PropertyTypes.h"
#include <functional>
#include <memory>

namespace PropertySystem::Observer {

// ==================================================================================
// 前向声明
// ==================================================================================

struct PropertyObserverPrivate;
struct PropertyObserverPointer;

// ==================================================================================
// 属性观察者基类
// ==================================================================================

/**
 * @brief 属性观察者基类
 * 
 * 这个类提供了观察属性变化的基础功能，
 * 支持自动注册和注销观察者。
 */
class PropertyObserverBase {
public:
    /**
     * @brief 默认构造函数
     */
    PropertyObserverBase();

    /**
     * @brief 移动构造函数
     * @param other 另一个观察者对象
     */
    PropertyObserverBase(PropertyObserverBase &&other) noexcept;

    /**
     * @brief 移动赋值运算符
     * @param other 另一个观察者对象
     * @return 当前对象的引用
     */
    PropertyObserverBase &operator=(PropertyObserverBase &&other) noexcept;

    /**
     * @brief 析构函数
     */
    ~PropertyObserverBase();

    /**
     * @brief 检查观察者是否处于活动状态
     * @return 如果观察者活动则返回true
     */
    bool isActive() const;

    /**
     * @brief 设置观察者的活动状态
     * @param active 是否激活观察者
     */
    void setActive(bool active);

protected:
    PropertyObserverPrivate *d_ptr = nullptr;

private:
    // 禁用拷贝构造和拷贝赋值
    PropertyObserverBase(const PropertyObserverBase&) = delete;
    PropertyObserverBase& operator=(const PropertyObserverBase&) = delete;
};

// ==================================================================================
// 标准属性观察者
// ==================================================================================

/**
 * @brief 标准属性观察者
 * 
 * 这个类提供了观察特定属性变化的功能，
 * 可以绑定到任何Property对象。
 */
class PropertyObserver : public PropertyObserverBase {
public:
    /**
     * @brief 默认构造函数
     */
    PropertyObserver() = default;

    /**
     * @brief 从属性构造观察者
     * @tparam PropertyType 属性类型
     * @param property 要观察的属性指针
     */
    template<typename PropertyType>
    PropertyObserver(PropertyType *property) {
        setSource(property);
    }

    /**
     * @brief 设置要观察的属性
     * @tparam PropertyType 属性类型
     * @param property 要观察的属性指针
     */
    template<typename PropertyType>
    void setSource(PropertyType *property) {
        setSource(static_cast<Core::UntypedPropertyData*>(property));
    }

    /**
     * @brief 设置要观察的未类型化属性
     * @param property 要观察的属性指针
     * @return 设置是否成功
     */
    bool setSource(Core::UntypedPropertyData *property);

    /**
     * @brief 清除观察的属性
     */
    void clearSource();

    /**
     * @brief 获取当前观察的属性
     * @return 属性指针，如果没有观察任何属性则返回nullptr
     */
    Core::UntypedPropertyData* source() const;
};

// ==================================================================================
// 属性变化处理器
// ==================================================================================

/**
 * @brief 属性变化处理器模板类
 * @tparam Handler 处理器函数类型
 * 
 * 这个类将观察者与特定的变化处理函数结合，
 * 提供类型安全的属性变化回调。
 */
template<typename Handler>
class PropertyChangeHandler : public PropertyObserver {
private:
    Handler handler_;

public:
    /**
     * @brief 构造函数
     * @tparam PropertyType 属性类型
     * @param property 要观察的属性
     * @param handler 变化处理函数
     */
    template<typename PropertyType>
    PropertyChangeHandler(PropertyType* property, Handler&& handler)
        : PropertyObserver(property), handler_(std::forward<Handler>(handler)) {}

    /**
     * @brief 构造函数（仅处理器）
     * @param handler 变化处理函数
     */
    explicit PropertyChangeHandler(Handler&& handler)
        : handler_(std::forward<Handler>(handler)) {}

    /**
     * @brief 获取处理器函数
     * @return 处理器函数的引用
     */
    const Handler& getHandler() const { return handler_; }

    /**
     * @brief 调用处理器函数
     * @tparam Args 参数类型
     * @param args 传递给处理器的参数
     */
    template<typename... Args>
    void invoke(Args&&... args) {
        if constexpr (std::is_invocable_v<Handler, Args...>) {
            handler_(std::forward<Args>(args)...);
        }
    }
};

// ==================================================================================
// 便利函数
// ==================================================================================

/**
 * @brief 创建属性观察者的便利函数
 * @tparam PropertyType 属性类型
 * @param property 要观察的属性
 * @return PropertyObserver对象
 */
template<typename PropertyType>
PropertyObserver makePropertyObserver(PropertyType* property) {
    return PropertyObserver(property);
}

/**
 * @brief 创建属性变化处理器的便利函数
 * @tparam PropertyType 属性类型
 * @tparam Handler 处理器类型
 * @param property 要观察的属性
 * @param handler 变化处理函数
 * @return PropertyChangeHandler对象
 */
template<typename PropertyType, typename Handler>
auto makePropertyChangeHandler(PropertyType* property, Handler&& handler) {
    return PropertyChangeHandler<std::remove_reference_t<Handler>>(
        property, std::forward<Handler>(handler));
}

/**
 * @brief 创建lambda观察者的便利函数
 * @tparam PropertyType 属性类型
 * @tparam Lambda lambda函数类型
 * @param property 要观察的属性
 * @param lambda lambda函数
 * @return PropertyChangeHandler对象
 */
template<typename PropertyType, typename Lambda>
auto observeProperty(PropertyType* property, Lambda&& lambda) {
    return makePropertyChangeHandler(property, std::forward<Lambda>(lambda));
}

// ==================================================================================
// 观察者状态管理
// ==================================================================================

/**
 * @brief 观察者状态管理器
 * 
 * 这个类提供了批量管理多个观察者状态的功能。
 */
class ObserverStateManager {
private:
    std::vector<PropertyObserverBase*> observers_;

public:
    /**
     * @brief 添加观察者到管理器
     * @param observer 观察者指针
     */
    void addObserver(PropertyObserverBase* observer);

    /**
     * @brief 从管理器移除观察者
     * @param observer 观察者指针
     */
    void removeObserver(PropertyObserverBase* observer);

    /**
     * @brief 激活所有观察者
     */
    void activateAll();

    /**
     * @brief 停用所有观察者
     */
    void deactivateAll();

    /**
     * @brief 清除所有观察者
     */
    void clear();

    /**
     * @brief 获取观察者数量
     * @return 观察者数量
     */
    size_t count() const { return observers_.size(); }
};

} // namespace PropertySystem::Observer

// ==================================================================================
// 全局命名空间别名（向后兼容）
// ==================================================================================

using PropertySystem::Observer::PropertyObserverBase;
using PropertySystem::Observer::PropertyObserver;
using PropertySystem::Observer::PropertyChangeHandler;
using PropertySystem::Observer::ObserverStateManager;

// 便利函数别名
using PropertySystem::Observer::makePropertyObserver;
using PropertySystem::Observer::makePropertyChangeHandler;
using PropertySystem::Observer::observeProperty;

#endif // PROPERTY_SYSTEM_OBSERVER_PROPERTY_OBSERVER_H
