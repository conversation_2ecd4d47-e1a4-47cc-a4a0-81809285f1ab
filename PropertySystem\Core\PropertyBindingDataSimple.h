/**
 * @file Core/PropertyBindingDataSimple.h
 * @brief 简化的属性绑定数据
 */

#ifndef PROPERTY_SYSTEM_CORE_PROPERTY_BINDING_DATA_SIMPLE_H
#define PROPERTY_SYSTEM_CORE_PROPERTY_BINDING_DATA_SIMPLE_H

namespace PropertySystem::Core::Internal {

/**
 * @brief 简化的属性绑定数据类
 * 
 * 这是一个简化版本，用于测试API结构。
 */
class PropertyBindingData {
public:
    /**
     * @brief 默认构造函数
     */
    PropertyBindingData() = default;
    
    /**
     * @brief 析构函数
     */
    ~PropertyBindingData() = default;
    
    /**
     * @brief 检查是否有绑定
     */
    bool hasBinding() const { return false; }
    
    /**
     * @brief 移除绑定（除非在包装器中）
     */
    void removeBindingUnlessInWrapper() {
        // 简化实现：暂时为空
    }
    
    // 禁用拷贝和移动
    PropertyBindingData(const PropertyBindingData&) = delete;
    PropertyBindingData(PropertyBindingData&&) = delete;
    PropertyBindingData& operator=(const PropertyBindingData&) = delete;
    PropertyBindingData& operator=(PropertyBindingData&&) = delete;
};

} // namespace PropertySystem::Core::Internal

#endif // PROPERTY_SYSTEM_CORE_PROPERTY_BINDING_DATA_SIMPLE_H
