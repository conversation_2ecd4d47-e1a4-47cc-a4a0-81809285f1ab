/**
 * @file test_core_api_simple.cpp
 * @brief Property系统核心API简化测试
 */

#include "PropertySystem/Core/Property.h"
#include <iostream>

using namespace PropertySystem::Core;

int main() {
    std::cout << "=== Property Core API Simple Test ===" << std::endl;
    
    try {
        // 1. 测试Property基础构造和value()方法
        std::cout << "\n1. Testing Property construction and value() method:" << std::endl;
        Property<int> intProp(42);
        int value = intProp.value();
        std::cout << "   Property<int> initial value: " << value << std::endl;
        
        // 2. 测试setValue()方法
        std::cout << "\n2. Testing setValue() method:" << std::endl;
        intProp.setValue(100);
        value = intProp.value();
        std::cout << "   After setValue(100): " << value << std::endl;
        
        // 3. 测试hasBinding()方法
        std::cout << "\n3. Testing hasBinding() method:" << std::endl;
        bool hasBinding = intProp.hasBinding();
        std::cout << "   hasBinding() initially: " << (hasBinding ? "true" : "false") << std::endl;
        
        // 4. 测试setBinding()方法
        std::cout << "\n4. Testing setBinding() method:" << std::endl;
        auto lambda = []() { return 200; };
        auto oldBinding = intProp.setBinding(lambda);
        std::cout << "   setBinding() executed successfully" << std::endl;
        
        // 5. 测试绑定后的hasBinding()状态
        hasBinding = intProp.hasBinding();
        std::cout << "   hasBinding() after setBinding: " << (hasBinding ? "true" : "false") << std::endl;
        
        // 6. 测试takeBinding()方法
        std::cout << "\n5. Testing takeBinding() method:" << std::endl;
        auto takenBinding = intProp.takeBinding();
        std::cout << "   takeBinding() executed successfully" << std::endl;
        
        // 7. 测试移除绑定后的状态
        hasBinding = intProp.hasBinding();
        std::cout << "   hasBinding() after takeBinding: " << (hasBinding ? "true" : "false") << std::endl;
        
        // 8. 测试字符串Property
        std::cout << "\n6. Testing Property<string>:" << std::endl;
        Property<std::string> stringProp;
        stringProp.setValue("Hello");
        std::cout << "   Property<string> value: " << stringProp.value() << std::endl;
        
        // 9. 测试字符串Property的绑定
        auto stringLambda = []() { return std::string("Bound Value"); };
        stringProp.setBinding(stringLambda);
        std::cout << "   Property<string> setBinding() executed successfully" << std::endl;
        
        std::cout << "\n=== All Core API Tests Completed Successfully! ===" << std::endl;
        std::cout << "\nCore API Methods Tested:" << std::endl;
        std::cout << "✓ Property::value() - Get property value" << std::endl;
        std::cout << "✓ Property::setValue() - Set property value" << std::endl;
        std::cout << "✓ Property::hasBinding() - Check if property has binding" << std::endl;
        std::cout << "✓ Property::setBinding() - Set property binding" << std::endl;
        std::cout << "✓ Property::takeBinding() - Remove and return binding" << std::endl;
        
        return 0;
        
    } catch (const std::exception& e) {
        std::cerr << "Error: " << e.what() << std::endl;
        return 1;
    } catch (...) {
        std::cerr << "Unknown error occurred" << std::endl;
        return 1;
    }
}
