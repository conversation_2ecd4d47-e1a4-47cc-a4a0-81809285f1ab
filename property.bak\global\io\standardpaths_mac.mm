﻿#ifdef __APPLE__

#include "standardpaths.h"

#import <Foundation/Foundation.h>
#include <stdexcept>
#include <filesystem>
#include <cstdlib>
#include <algorithm>
#include <system_error>

std::string StandardPaths::getPath(DirectoryType type) {
    @autoreleasepool {
        NSSearchPathDirectory dir = NSDocumentDirectory;
        switch (type) {
            case DirectoryType::Home:      return [NSHomeDirectory() UTF8String];
            case DirectoryType::Documents:dir = NSDocumentDirectory; break;
            case DirectoryType::Downloads:dir = NSDownloadsDirectory; break;
            case DirectoryType::Music:    dir = NSMusicDirectory; break;
            case DirectoryType::Pictures: dir = NSPicturesDirectory; break;
            // case DirectoryType::Videos:    dir = NSVideosDirectory; break;
            case DirectoryType::Desktop:  dir = NSDesktopDirectory; break;
            case DirectoryType::AppData:  return std::string([NSHomeDirectory() UTF8String]) + "/Library/Application Support/";

            case DirectoryType::Temp:
                return formatPath(std::filesystem::temp_directory_path().string());
            case DirectoryType::Current:
                return formatPath(std::filesystem::current_path().string());
            case DirectoryType::Cache:     return formatPath([NSHomeDirectory() UTF8String] + "/Library/Caches");
            case DirectoryType::Config:    return formatPath([NSHomeDirectory() UTF8String] + "/Library/Preferences");
            default:
                throw std::invalid_argument("Unsupported directory type");
        }

        NSArray* paths = NSSearchPathForDirectoriesInDomains(dir, NSUserDomainMask, YES);
        if ([paths count] == 0) {
            throw std::runtime_error("Failed to get system folder path");
        }

        NSString* path = [paths objectAtIndex:0];
        return std::string([path UTF8String]) + "/";
    }
}

std::vector<std::string> StandardPaths::standardLocations(DirectoryType type) {
    std::vector<std::string> paths;
    paths.push_back(getPath(type));

    // Add alternative locations if available
    switch (type) {
        case DirectoryType::AppData: {
            // Add system-wide application support directory
            paths.push_back(formatPath("/Library/Application Support"));
            break;
        }
        case DirectoryType::Config: {
            // Add system-wide preferences directory
            paths.push_back(formatPath("/Library/Preferences"));
            break;
        }
        default:
            break;
    }

    return paths;
}

// Helper method to format paths according to the requested format
std::string StandardPaths::formatPath(const std::string& path) {
    if (path.empty()) {
        return path;
    }

    std::string result = path;

    // Handle trailing separator based on format
    if (!result.empty() && result.back() != '/') {
        result += '/';
    }

    return result;
}

#endif
