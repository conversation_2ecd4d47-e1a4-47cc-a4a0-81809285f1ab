/**
 * @file Core/PropertyUpdateGroup.cpp
 * @brief 属性更新组实现
 *
 * 这个文件提供了Property系统更新组的具体实现。
 */

#include "PropertyUpdateGroup.h"
#include <atomic>
#include <memory>

namespace PropertySystem::Core {

// ==================================================================================
// 全局状态管理
// ==================================================================================

namespace Internal {

UpdateGroupState& UpdateGroupState::global() {
    static UpdateGroupState instance;
    return instance;
}

PropertyDelayedNotifications* getCurrentDelayedNotifications() {
    return UpdateGroupState::global().notifications.get();
}

void setCurrentDelayedNotifications(PropertyDelayedNotifications *notifications) {
    auto& state = UpdateGroupState::global();
    state.notifications.reset(notifications);
}

void processDelayedNotifications() {
    auto& state = UpdateGroupState::global();
    auto notifications = std::move(state.notifications);
    
    if (!notifications) {
        return;
    }
    
    // 处理所有延迟的通知
    auto* current = notifications.get();
    while (current) {
        for (size_t i = 0; i < current->used; ++i) {
            current->notify(i);
            current->cleanup(i);
        }
        current = current->next.get();
    }
}

// PropertyDelayedNotifications简化实现
void PropertyDelayedNotifications::addProperty(const PropertyBindingData *bindingData,
                                              UntypedPropertyData *propertyData) {
    // 简化实现：暂时为空
}

void PropertyDelayedNotifications::evaluateBindings(size_t index,
                                                   std::vector<class PropertyObserver*> &bindingObservers,
                                                   struct BindingEvaluationState *status) {
    // 简化实现：暂时为空
}

void PropertyDelayedNotifications::notify(size_t index) {
    // 简化实现：暂时为空
}

void PropertyDelayedNotifications::cleanup(size_t index) {
    // 简化实现：暂时为空
}

} // namespace Internal

// ==================================================================================
// 更新组管理函数实现
// ==================================================================================

void beginPropertyUpdateGroup() {
    auto& state = Internal::UpdateGroupState::global();
    int oldDepth = state.depth.fetch_add(1, std::memory_order_relaxed);
    
    // 如果是第一层，创建延迟通知对象
    if (oldDepth == 0) {
        auto notifications = std::make_unique<Internal::PropertyDelayedNotifications>();
        state.notifications = std::move(notifications);
    }
}

void endPropertyUpdateGroup() {
    auto& state = Internal::UpdateGroupState::global();
    int newDepth = state.depth.fetch_sub(1, std::memory_order_relaxed) - 1;
    
    if (newDepth < 0) {
        // 错误：没有匹配的beginPropertyUpdateGroup调用
        state.depth.store(0, std::memory_order_relaxed);
        return;
    }
    
    // 如果是最后一层，处理所有延迟通知
    if (newDepth == 0) {
        Internal::processDelayedNotifications();
    }
}

bool isInPropertyUpdateGroup() {
    auto& state = Internal::UpdateGroupState::global();
    return state.depth.load(std::memory_order_relaxed) > 0;
}

int getPropertyUpdateGroupDepth() {
    auto& state = Internal::UpdateGroupState::global();
    return state.depth.load(std::memory_order_relaxed);
}

} // namespace PropertySystem::Core
