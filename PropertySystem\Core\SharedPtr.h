#ifndef PROPERTY_SYSTEM_CORE_SHARED_PTR_H
#define PROPERTY_SYSTEM_CORE_SHARED_PTR_H

#include <memory>
#include <atomic>
#include <type_traits>
#include "Concepts.h"

/**
 * @file PropertySystem/Core/SharedPtr.h
 * @brief Modern C++20 shared pointer implementation using std::shared_ptr
 * 
 * This file provides a modern replacement for the custom SharedDataPointer
 * implementation, using std::shared_ptr as the underlying mechanism while
 * maintaining API compatibility.
 */

namespace PropertySystem::Core {

// ==================================================================================
// Base Classes for Shared Data
// ==================================================================================

/**
 * @brief Modern replacement for SharedData base class
 * 
 * This class provides a simple base for objects that need to be managed
 * by shared pointers. Unlike the original SharedData, this doesn't need
 * custom reference counting as std::shared_ptr handles that.
 */
class SharedDataBase {
public:
    SharedDataBase() = default;
    SharedDataBase(const SharedDataBase&) = default;
    SharedDataBase& operator=(const SharedDataBase&) = default;
    SharedDataBase(SharedDataBase&&) = default;
    SharedDataBase& operator=(SharedDataBase&&) = default;
    virtual ~SharedDataBase() = default;
    
    /**
     * @brief Get the current reference count
     * @return Reference count (for debugging/compatibility)
     * @note This is provided for compatibility but may not be accurate
     *       in multi-threaded scenarios due to std::shared_ptr limitations
     */
    long use_count() const noexcept {
        // This is a placeholder - actual use_count comes from shared_ptr
        return 1;
    }
};

// ==================================================================================
// Modern Shared Pointer Implementation
// ==================================================================================

/**
 * @brief Modern shared pointer wrapper using std::shared_ptr
 * @tparam T Element type
 * 
 * This class provides API compatibility with the original SharedDataPointer
 * while using std::shared_ptr internally for better standard library integration.
 */
template<typename T>
requires PropertyType<T>
class ModernSharedPtr {
private:
    std::shared_ptr<T> ptr_;

public:
    using element_type = T;
    using pointer = T*;
    using const_pointer = const T*;
    using reference = T&;
    using const_reference = const T&;

    // ==================================================================================
    // Constructors and Destructor
    // ==================================================================================
    
    /**
     * @brief Default constructor - creates empty pointer
     */
    constexpr ModernSharedPtr() noexcept = default;
    
    /**
     * @brief Nullptr constructor
     */
    constexpr ModernSharedPtr(std::nullptr_t) noexcept : ptr_(nullptr) {}
    
    /**
     * @brief Constructor from raw pointer
     * @param p Raw pointer to take ownership of
     */
    explicit ModernSharedPtr(T* p) : ptr_(p) {}
    
    /**
     * @brief Constructor from std::shared_ptr
     * @param sp Shared pointer to copy
     */
    ModernSharedPtr(std::shared_ptr<T> sp) noexcept : ptr_(std::move(sp)) {}
    
    /**
     * @brief Copy constructor
     * @param other Other ModernSharedPtr to copy
     */
    ModernSharedPtr(const ModernSharedPtr& other) noexcept = default;
    
    /**
     * @brief Move constructor
     * @param other Other ModernSharedPtr to move from
     */
    ModernSharedPtr(ModernSharedPtr&& other) noexcept = default;
    
    /**
     * @brief Converting copy constructor
     * @tparam U Compatible type
     * @param other Other ModernSharedPtr to copy
     */
    template<typename U>
    requires std::convertible_to<U*, T*>
    ModernSharedPtr(const ModernSharedPtr<U>& other) noexcept 
        : ptr_(other.get_shared_ptr()) {}
    
    /**
     * @brief Converting move constructor
     * @tparam U Compatible type
     * @param other Other ModernSharedPtr to move from
     */
    template<typename U>
    requires std::convertible_to<U*, T*>
    ModernSharedPtr(ModernSharedPtr<U>&& other) noexcept 
        : ptr_(std::move(other.get_shared_ptr())) {}

    // ==================================================================================
    // Assignment Operators
    // ==================================================================================
    
    /**
     * @brief Copy assignment operator
     * @param other Other ModernSharedPtr to copy
     * @return Reference to this
     */
    ModernSharedPtr& operator=(const ModernSharedPtr& other) noexcept = default;
    
    /**
     * @brief Move assignment operator
     * @param other Other ModernSharedPtr to move from
     * @return Reference to this
     */
    ModernSharedPtr& operator=(ModernSharedPtr&& other) noexcept = default;
    
    /**
     * @brief Nullptr assignment
     * @return Reference to this
     */
    ModernSharedPtr& operator=(std::nullptr_t) noexcept {
        ptr_.reset();
        return *this;
    }

    // ==================================================================================
    // Access Operations
    // ==================================================================================
    
    /**
     * @brief Dereference operator
     * @return Reference to the pointed-to object
     */
    reference operator*() const noexcept {
        return *ptr_;
    }
    
    /**
     * @brief Arrow operator
     * @return Pointer to the pointed-to object
     */
    pointer operator->() const noexcept {
        return ptr_.get();
    }
    
    /**
     * @brief Get raw pointer
     * @return Raw pointer to the managed object
     */
    pointer get() const noexcept {
        return ptr_.get();
    }
    
    /**
     * @brief Get the underlying std::shared_ptr
     * @return Reference to the underlying std::shared_ptr
     */
    const std::shared_ptr<T>& get_shared_ptr() const noexcept {
        return ptr_;
    }
    
    /**
     * @brief Get the underlying std::shared_ptr (non-const)
     * @return Reference to the underlying std::shared_ptr
     */
    std::shared_ptr<T>& get_shared_ptr() noexcept {
        return ptr_;
    }

    // ==================================================================================
    // State Queries
    // ==================================================================================
    
    /**
     * @brief Check if pointer is not null
     * @return true if pointer is not null
     */
    explicit operator bool() const noexcept {
        return static_cast<bool>(ptr_);
    }
    
    /**
     * @brief Check if pointer is unique
     * @return true if this is the only reference
     */
    bool unique() const noexcept {
        return ptr_.unique();
    }
    
    /**
     * @brief Get reference count
     * @return Number of shared references
     */
    long use_count() const noexcept {
        return ptr_.use_count();
    }

    // ==================================================================================
    // Modifiers
    // ==================================================================================
    
    /**
     * @brief Reset pointer to null
     */
    void reset() noexcept {
        ptr_.reset();
    }
    
    /**
     * @brief Reset pointer to new value
     * @param p New pointer value
     */
    void reset(T* p) {
        ptr_.reset(p);
    }
    
    /**
     * @brief Swap with another ModernSharedPtr
     * @param other Other pointer to swap with
     */
    void swap(ModernSharedPtr& other) noexcept {
        ptr_.swap(other.ptr_);
    }

    // ==================================================================================
    // Comparison Operators
    // ==================================================================================
    
    /**
     * @brief Equality comparison
     */
    bool operator==(const ModernSharedPtr& other) const noexcept {
        return ptr_ == other.ptr_;
    }
    
    /**
     * @brief Inequality comparison
     */
    bool operator!=(const ModernSharedPtr& other) const noexcept {
        return ptr_ != other.ptr_;
    }
    
    /**
     * @brief Less-than comparison
     */
    bool operator<(const ModernSharedPtr& other) const noexcept {
        return ptr_ < other.ptr_;
    }
    
    /**
     * @brief Nullptr equality comparison
     */
    bool operator==(std::nullptr_t) const noexcept {
        return ptr_ == nullptr;
    }
    
    /**
     * @brief Nullptr inequality comparison
     */
    bool operator!=(std::nullptr_t) const noexcept {
        return ptr_ != nullptr;
    }
};

// ==================================================================================
// Factory Functions
// ==================================================================================

/**
 * @brief Create a shared pointer with arguments
 * @tparam T Type to create
 * @tparam Args Argument types
 * @param args Arguments to forward to constructor
 * @return ModernSharedPtr to the created object
 */
template<typename T, typename... Args>
requires PropertyType<T>
ModernSharedPtr<T> makeShared(Args&&... args) {
    return ModernSharedPtr<T>(std::make_shared<T>(std::forward<Args>(args)...));
}

/**
 * @brief Swap two ModernSharedPtr objects
 * @tparam T Element type
 * @param lhs First pointer
 * @param rhs Second pointer
 */
template<typename T>
void swap(ModernSharedPtr<T>& lhs, ModernSharedPtr<T>& rhs) noexcept {
    lhs.swap(rhs);
}

} // namespace PropertySystem::Core

// ==================================================================================
// 全局命名空间别名
// ==================================================================================

using PropertySystem::Core::ModernSharedPtr;
using PropertySystem::Core::SharedDataBase;
using PropertySystem::Core::makeShared;

#endif // PROPERTY_SYSTEM_CORE_SHARED_PTR_H
