﻿#ifndef FILE_H
#define FILE_H

#include <filesystem>
#include <string>
#include <fstream>
#include <memory>

#include "iodevice.h"
#include "path.h"

namespace filesystem {

/**
 * @brief The File class provides an interface for reading from and writing to files.
 * 
 * File is an IODevice that operates on files, allowing reading from and writing to files.
 */
class File : public IODevice {
public:
    /**
     * @brief Constructs a File with no file name.
     */
    File();

    /**
     * @brief Constructs a File with the given file name.
     * @param fileName The name of the file.
     */
    explicit File(const std::string& fileName);

    /**
     * @brief Constructs a File with the given file name.
     * @param fileName The name of the file.
     */
    explicit File(const Path& fileName);

    /**
     * @brief Destructor.
     */
    ~File() override;

    /**
     * @brief Opens the file with the specified mode.
     * @param mode The open mode.
     * @return True if the file was opened successfully, false otherwise.
     */
    bool open(OpenMode mode) override;

    /**
     * @brief Closes the file.
     */
    void close() override;

    /**
     * @brief Returns whether the file is open.
     * @return True if the file is open, false otherwise.
     */
    bool isOpen() const override;

    /**
     * @brief Returns the open mode of the file.
     * @return The open mode.
     */
    OpenMode openMode() const override;

    /**
     * @brief Returns the size of the file.
     * @return The size in bytes, or -1 if the size is unknown.
     */
    int64_t size() const override;

    /**
     * @brief Returns the current position in the file.
     * @return The current position, or -1 if the position is unknown.
     */
    int64_t pos() const override;

    /**
     * @brief Sets the current position in the file.
     * @param pos The new position.
     * @param origin The origin from which to seek.
     * @return The new position, or -1 if an error occurred.
     */
    int64_t seek(int64_t pos, SeekOrigin origin = SeekOrigin::Begin) override;

    /**
     * @brief Returns whether the file is at the end.
     * @return True if the file is at the end, false otherwise.
     */
    bool atEnd() const override;

    /**
     * @brief Reads data from the file.
     * @param data The buffer to read into.
     * @param maxSize The maximum number of bytes to read.
     * @return The number of bytes read, or -1 if an error occurred.
     */
    int64_t read(char* data, int64_t maxSize) override;

    /**
     * @brief Writes data to the file.
     * @param data The data to write.
     * @param size The number of bytes to write.
     * @return The number of bytes written, or -1 if an error occurred.
     */
    int64_t write(const char* data, int64_t size) override;

    /**
     * @brief Flushes any buffered data to the file.
     * @return True if the data was flushed successfully, false otherwise.
     */
    bool flush() override;

    /**
     * @brief Returns the error string for the last error.
     * @return The error string, or an empty string if no error occurred.
     */
    std::string errorString() const override;

    /**
     * @brief Returns the file name.
     * @return The file name.
     */
    std::string fileName() const;

    /**
     * @brief Sets the file name.
     * @param fileName The new file name.
     */
    void setFileName(const std::string& fileName);

    /**
     * @brief Sets the file name.
     * @param fileName The new file name.
     */
    void setFileName(const Path& fileName);

    /**
     * @brief Returns whether the file exists.
     * @return True if the file exists, false otherwise.
     */
    bool exists() const;

    /**
     * @brief Removes the file.
     * @return True if the file was removed successfully, false otherwise.
     */
    bool remove();

    /**
     * @brief Renames the file.
     * @param newName The new name for the file.
     * @return True if the file was renamed successfully, false otherwise.
     */
    bool rename(const std::string& newName);

    /**
     * @brief Copies the file.
     * @param newName The name for the copy.
     * @return True if the file was copied successfully, false otherwise.
     */
    bool copy(const std::string& newName);

    /**
     * @brief Returns the permissions of the file.
     * @return The permissions as a bitmask.
     */
    std::filesystem::perms permissions() const;

    /**
     * @brief Sets the permissions of the file.
     * @param perms The new permissions.
     * @return True if the permissions were set successfully, false otherwise.
     */
    bool setPermissions(std::filesystem::perms perms);

private:
    std::string m_fileName;
    std::unique_ptr<std::fstream> m_file;
    mutable std::string m_errorString;
    
    // Convert IODevice::OpenMode to std::ios_base::openmode
    std::ios_base::openmode convertOpenMode(OpenMode mode) const;
};

} // namespace filesystem

#endif // FILE_H
