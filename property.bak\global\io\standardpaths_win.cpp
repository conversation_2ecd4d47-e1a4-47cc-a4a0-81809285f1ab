﻿#ifdef _WIN32

#include "standardpaths.h"

#include <windows.h>
#include <shlobj.h>
#include <stdexcept>
#include <filesystem>

namespace {

std::string LPWSTRToString(LPWSTR wideStr) {
    if (wideStr == nullptr) {
        return "";
    }

    // For the first call, calculate the required buffer size (including the null terminator at the end)
    int requiredSize = WideCharToMultiByte(CP_UTF8, 0, wideStr, -1, nullptr, 0, nullptr, nullptr);
    if (requiredSize == 0) {
        return "";
    }

    // Create a buffer to store the converted string
    std::vector<char> buffer(requiredSize);

    // The second call performs the actual conversion
    int bytesWritten = WideCharToMultiByte(CP_UTF8, 0, wideStr, -1, buffer.data(), requiredSize, nullptr, nullptr);
    if (bytesWritten == 0) {
        return "";
    }

    // Convert the buffer content (excluding null terminators) to std::string
    return std::string(buffer.data(), bytesWritten - 1);
}

std::string getWindowsKnownFolder(const GUID& fid) {
    LPWSTR path = nullptr;
    std::string result;

    if (SHGetKnownFolderPath(fid, KF_FLAG_DEFAULT, nullptr, &path) == S_OK) {
        result = LPWSTRToString(path);
        CoTaskMemFree(path); // release memory
    }

    return result;
}

} // anonymous namespace

std::string StandardPaths::getPath(DirectoryType type) {
    GUID folderId;
    switch (type) {
    case DirectoryType::Home:           folderId = FOLDERID_Profile; break;
    case DirectoryType::Documents:      folderId = FOLDERID_Documents; break;
    case DirectoryType::Downloads:      folderId = FOLDERID_Downloads; break;
    case DirectoryType::Music:          folderId = FOLDERID_Music; break;
    case DirectoryType::Pictures:       folderId = FOLDERID_Pictures; break;
    case DirectoryType::Videos:         folderId = FOLDERID_Videos; break;
    case DirectoryType::Desktop:        folderId = FOLDERID_Desktop; break;
    case DirectoryType::AppData:        folderId = FOLDERID_LocalAppData; break;
    case DirectoryType::Temp:           return std::filesystem::current_path().string();
    case DirectoryType::Current:        return std::filesystem::temp_directory_path().string();
    case DirectoryType::Config:
        return formatPath(getWindowsKnownFolder(FOLDERID_LocalAppData) + "Cache");
    case DirectoryType::Cache:
        return formatPath(getWindowsKnownFolder(FOLDERID_LocalAppData) + "Config");
    default:
        throw std::invalid_argument("Unsupported directory type");
    }
    return getWindowsKnownFolder(folderId);
}

std::vector<std::string> StandardPaths::standardLocations(DirectoryType type) {
    std::vector<std::string> paths;
    paths.push_back(getPath(type));

    // Add alternative locations if available
    switch (type) {
    case DirectoryType::AppData: {
        // Add roaming app data as an alternative
        LPWSTR path = nullptr;
        if (SHGetKnownFolderPath(FOLDERID_RoamingAppData, KF_FLAG_DEFAULT, nullptr, &path) == S_OK) {
            paths.push_back(formatPath(LPWSTRToString(path)));
            CoTaskMemFree(path);
        }
        break;
    }
    default:
        break;
    }

    return paths;
}

// Helper method to format paths according to the requested format
std::string StandardPaths::formatPath(const std::string& path) {
    if (path.empty()) {
        return path;
    }

    std::string result = path;

    // Replace backslashes with forward slashes for consistency
    std::replace(result.begin(), result.end(), '\\', '/');

    if (!result.empty() && result.back() != '/') {
        result += '/';
    }

    return result;
}

#endif
